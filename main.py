from PIL import Image, ImageDraw
import random
import os
from concurrent.futures import ThreadPoolExecutor
import time
import colorsys

# 配置参数
WIDTH = 1080
HEIGHT = 1920
OUTPUT_FOLDER = "vibrant_gradients"
TOTAL_IMAGES = 600
THREADS = 8  # 根据CPU核心数调整

# 创建输出目录
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def get_vibrant_color():
    """生成鲜艳的颜色（使用HSV色彩空间）"""
    h = random.random()  # 色相 0-1
    s = 0.7 + random.random() * 0.3  # 饱和度 0.7-1.0
    v = 0.8 + random.random() * 0.2  # 明度 0.8-1.0
    r, g, b = colorsys.hsv_to_rgb(h, s, v)
    return (int(r*255), int(g*255), int(b*255))

def generate_gradient_image(index):
    """生成单张鲜艳渐变图片"""
    color1 = get_vibrant_color()
    color2 = get_vibrant_color()
    
    img = Image.new("RGB", (WIDTH, HEIGHT))
    draw = ImageDraw.Draw(img)
    
    # 生成垂直渐变（可改为水平或其他方向）
    for y in range(HEIGHT):
        ratio = y / HEIGHT
        r = int(color1[0] + (color2[0] - color1[0]) * ratio)
        g = int(color1[1] + (color2[1] - color1[1]) * ratio)
        b = int(color1[2] + (color2[2] - color1[2]) * ratio)
        draw.line([(0, y), (WIDTH, y)], fill=(r, g, b))
    
    # 按%05d.png格式保存
    filename = os.path.join(OUTPUT_FOLDER, f"{index:05d}.png")
    img.save(filename, format="PNG")
    return filename

def batch_generate():
    print(f"开始生成 {TOTAL_IMAGES} 张鲜艳渐变背景图片...")
    start_time = time.time()
    
    # 使用线程池并行生成
    with ThreadPoolExecutor(max_workers=THREADS) as executor:
        results = list(executor.map(generate_gradient_image, range(TOTAL_IMAGES)))
    
    total_time = time.time() - start_time
    print("\n完成! 图片详情:")
    print(f"尺寸: {WIDTH}x{HEIGHT}")
    print(f"格式: PNG (无损)")
    print(f"命名: 00000.png 到 {TOTAL_IMAGES-1:05d}.png")
    print(f"总耗时: {total_time:.2f}秒")
    print(f"平均每张: {total_time/TOTAL_IMAGES:.3f}秒")
    print(f"输出目录: {os.path.abspath(OUTPUT_FOLDER)}")

if __name__ == "__main__":
    batch_generate()
