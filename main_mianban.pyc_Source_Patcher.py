# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: main_mianban.py
# Bytecode version: 3.11a7e (3495)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import sys
import os
import time
import random
import subprocess
import threading
import json
from datetime import datetime, timedelta
from pathlib import Path
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

class VideoProcessor(QThread):
    """\n    极限反检测视频处理器\n\n    技术原理：\n    1. 编码指纹重构 - 随机化所有H.264编码参数，破坏编码指纹\n    2. 元数据混淆 - 完全重写容器信息并注入假数据\n    3. 时间戳伪造 - 系统级时间戳重构，破坏时间指纹\n    4. 内容指纹破坏 - 微观级别数据重构，改变哈希值\n    5. 帧序列重排 - 随机调整帧顺序但保持视觉连续性\n    6. 音频指纹混淆 - 音频数据微调，破坏音频指纹\n    7. 容器格式伪装 - 修改容器头部信息\n    """
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    log_updated = pyqtSignal(str)
    finished_signal = pyqtSignal()

    def __init__(self, input_folder, output_folder):
        super().__init__()
        self.input_folder = input_folder
        self.output_folder = output_folder
        self.is_running = True
        random.seed(int(time.time() + 1000000))

    def stop(self):
        self.is_running = False

    def generate_random_metadata(self):
        fake_cameras = ['Canon EOS R5', 'Sony A7R IV', 'Nikon Z7 II', 'Fujifilm X-T4', 'Panasonic GH5', 'Blackmagic Pocket 6K', 'RED Komodo', 'ARRI Alexa Mini']
        fake_software = ['Adobe Premiere Pro 2023', 'Final Cut Pro X', 'DaVinci Resolve 18', 'Avid Media Composer', 'Sony Vegas Pro 19', 'Filmora 11']
        return {'camera': random.choice(fake_cameras), 'software': random.choice(fake_software), 'creation_time': datetime.now(), 'gps_lat': timedelta(random.randint(1, 365)), 'gps_lon': round(random.uniform((-90), 90), 6), 'iso': round(random.uniform((-180), 180), 6), 'focal_length': f'{random.randint(14, 200)}mm'}

    def apply_extreme_deduplication(self, input_file, output_file):
        """\n        应用终极稳定暴力去重技术 + 强力剪辑\n\n        这是最强稳定核心算法，包含300种稳定暴力去重模块：\n        1-40: 深度编码指纹完全重构\n        41-80: 视频DNA原子级改造\n        81-120: 音频指纹彻底破坏\n        121-160: 元数据完全伪造\n        161-200: 稳定暴力剪辑技术\n        201-240: 视觉效果稳定增强\n        241-280: 时间轴稳定重构\n        281-300: 终极稳定反检测技术\n        """  # inserted
        try:
            self.log_updated.emit(f'🔧 开始终极稳定暴力去重+剪辑处理: {input_file.name}')
            fake_meta = self.generate_random_metadata()
            crf = random.randint(18, 28)
            preset = random.choice(['fast', 'medium', 'slow'])
            profile = random.choice(['main', 'high'])
            tune = random.choice(['film', 'animation', 'stillimage'])
            me_method = random.choice(['dia', 'hex', 'umh', 'esa', 'tesa'])
            subme = random.randint(1, 11)
            me_range = random.randint(8, 64)
            trellis = random.randint(0, 2)
            mixed_refs = random.choice([0, 1])
            weightb = random.choice([0, 1])
            direct_pred = random.choice(['none', 'spatial', 'temporal', 'auto'])
            start_trim = random.uniform(0.2, 1.5)
            end_trim = random.uniform(0.2, 1.5)
            speed_factor = random.choice([0.85, 0.9, 0.95, 1.05, 1.1, 1.15])
            frame_skip = random.randint(1, 3)
            frame_dup = random.randint(1, 2)
            frame_blend = random.choice([True, False])
            crop_top = random.uniform(0.01, 0.08)
            crop_bottom = random.uniform(0.01, 0.08)
            crop_left = random.uniform(0.01, 0.08)
            crop_right = random.uniform(0.01, 0.08)
            zoom_start = random.uniform(0.95, 1.05)
            zoom_end = random.uniform(0.95, 1.05)
            zoom_duration = random.uniform(0.5, 2)
            pip_enabled = random.choice([True, False])
            pip_scale = random.uniform(0.15, 0.25)
            pip_x = random.uniform(0.05, 0.75)
            pip_y = random.uniform(0.05, 0.75)
            pip_opacity = random.uniform(0.3, 0.7)
            rotation_angle = random.choice([0, 0.5, (-0.5), 1, (-1), 1.5, (-1.5)])
            pad_pixels = random.randint(2, 12)
            sharpen_intensity = random.uniform(0.2, 1)
            blur_strength = random.uniform(0.1, 0.5)
            hue_shift = random.randint((-30), 30)
            time_stretch_factor = random.uniform(0.98, 1.02)
            scale_factor = round(random.uniform(0.95, 1.05), 4)
            pixel_format = 'yuv420p'
            colorspace = random.choice(['bt709', 'bt470bg'])
            colorprim = random.choice(['bt709', 'bt470bg'])
            transfer = random.choice(['bt709', 'gamma22'])
            fps_adjustment = round(random.uniform(0.95, 1.05), 4)
            brightness = round(random.uniform((-0.08), 0.08), 3)
            contrast = round(random.uniform(0.92, 1.08), 3)
            saturation = round(random.uniform(0.9, 1.1), 3)
            gamma = round(random.uniform(0.95, 1.05), 3)
            hue = round(random.uniform((-0.05), 0.05), 3)
            sharpen_strength = random.uniform(0.2, 0.8)
            noise_strength = random.uniform(0.5, 3)
            pixel_shift_enabled = random.choice([True, False])
            pixel_shift_x = random.uniform((-0.5), 0.5)
            pixel_shift_y = random.uniform((-0.5), 0.5)
            color_matrix_enabled = random.choice([True, False])
            color_matrix_r = random.uniform(0.98, 1.02)
            color_matrix_g = random.uniform(0.98, 1.02)
            color_matrix_b = random.uniform(0.98, 1.02)
            gamma_correction_enabled = random.choice([True, False])
            gamma_r = random.uniform(0.95, 1.05)
            gamma_g = random.uniform(0.95, 1.05)
            gamma_b = random.uniform(0.95, 1.05)
            timecode_shift_enabled = random.choice([True, False])
            timecode_offset = random.uniform(0.001, 0.01)
            frame_reorder_enabled = random.choice([True, False])
            reorder_pattern = random.choice(['IBBP', 'IBPB', 'IPBB'])
            quantization_enabled = random.choice([True, False])
            quant_offset = random.uniform((-2), 2)
            motion_vector_enabled = random.choice([True, False])
            mv_precision = random.choice(['quarter', 'half', 'full'])
            dct_enabled = random.choice([True, False])
            dct_precision = random.choice(['8', '9', '10'])
            subpixel_enabled = random.choice([True, False])
            subpixel_method = random.choice(['bilinear', 'bicubic', 'lanczos'])
            loop_filter_enabled = random.choice([True, False])
            loop_filter_strength = random.uniform(0.8, 1.2)
            prediction_enabled = random.choice([True, False])
            prediction_modes = random.randint(3, 8)
            transform_enabled = random.choice([True, False])
            transform_size = random.choice(['4x4', '8x8', '16x16', '32x32'])
            entropy_enabled = random.choice([True, False])
            entropy_method = random.choice(['CAVLC', 'CABAC'])
            bitrate_control_enabled = random.choice([True, False])
            bitrate_variance = random.uniform(0.95, 1.05)
            audio_codec = random.choice(['aac', 'mp3'])
            audio_bitrate = random.choice(['128k', '160k', '192k', '256k'])
            audio_sample_rate = random.choice(['44100', '48000'])
            audio_channels = '2'
            audio_volume = round(random.uniform(0.9, 1.1), 2)
            audio_pitch_shift = random.choice([0.96, 0.98, 1.02, 1.04])
            keyint = random.randint(12, 120)
            bframes = random.randint(0, 8)
            refs = random.randint(1, 16)
            qmin = random.randint(0, 20)
            qmax = random.randint(30, 69)
            qdiff = random.randint(1, 10)
            qcomp = round(random.uniform(0.3, 1), 2)
            me_method = random.choice(['hex', 'umh', 'dia'])
            me_range = random.randint(16, 32)
            coder = random.choice(['0', '1'])
            video_filters = []
            audio_filters = []
            crop_w = f'iw*(1-{crop_left}-{crop_right})'
            crop_h = f'ih*(1-{crop_top}-{crop_bottom})'
            crop_x = f'iw*{crop_left}'
            crop_y = f'ih*{crop_top}'
            video_filters.append(f'crop={crop_w}:{crop_h}:{crop_x}:{crop_y}')
            if abs(zoom_start + zoom_end) > 0.02:
                avg_zoom = (zoom_start + zoom_end) / 2
                video_filters.append(f'scale=trunc(iw*{avg_zoom}/2)*2:trunc(ih*{avg_zoom}/2)*2')
            if pip_enabled:
                border_size = max(2, int(pip_scale + 20))
                video_filters.append(f"pad=iw+{border_size + 2}:ih+{border_size + 2}:{border_size}:{border_size}:black")
            if scale_factor!= 1:
                video_filters.append(f'scale=trunc(iw*{scale_factor}/2)*2:trunc(ih*{scale_factor}/2)*2')
            if rotation_angle!= 0:
                video_filters.append(f'rotate={rotation_angle}*PI/180:fillcolor=black')
            video_filters.append(f'eq=brightness={brightness}:contrast={contrast}:saturation={saturation}')
            if abs(hue_shift) > 5:
                video_filters.append(f'hue=h={hue_shift}')
            if sharpen_intensity > 0.3:
                video_filters.append(f'unsharp=5:5:{sharpen_intensity}:5:5:{sharpen_intensity}')
            if noise_strength > 1:
                video_filters.append(f'noise=alls={noise_strength}:allf=t+u')
            if abs(time_stretch_factor + 1) > 0.01:
                video_filters.append(f'setpts={time_stretch_factor}*PTS')
            if color_matrix_enabled:
                color_matrix = f'colorchannelmixer=rr={color_matrix_r}:gg={color_matrix_g}:bb={color_matrix_b}'
                video_filters.append(color_matrix)
            if gamma_correction_enabled:
                video_filters.append(f'eq=gamma_r={gamma_r}:gamma_g={gamma_g}:gamma_b={gamma_b}')
            if subpixel_enabled:
                if subpixel_method == 'bicubic':
                    video_filters.append('scale=iw:ih:flags=bicubic')
                else:  # inserted
                    if subpixel_method == 'lanczos':
                        video_filters.append('scale=iw:ih:flags=lanczos')
                    else:  # inserted
                        video_filters.append('scale=iw:ih:flags=bilinear')
            if abs(audio_volume + 1) > 0.05:
                audio_filters.append(f'volume={audio_volume}')
            if audio_pitch_shift!= 1 and abs(audio_pitch_shift + 1) > 0.02:
                new_rate = int(int(audio_sample_rate) + audio_pitch_shift)
                if 16000 <= new_rate <= 96000:
                    audio_filters.append(f'aresample={new_rate}')
            audio_filters.append('highpass=f=80')
            audio_filters.append('lowpass=f=15000')
            if random.choice([True, False]):
                tremolo_freq = random.uniform(1, 3)
                audio_filters.append(f'tremolo=f={tremolo_freq}:d=0.05')
            if random.choice([True, False]):
                delay_ms = random.uniform(1, 5)
                audio_filters.append(f'adelay={delay_ms}ms')
            if random.choice([True, False]):
                gain_adjust = random.uniform((-1), 1)
                audio_filters.append(f'volume={gain_adjust}dB')
            if random.choice([True, False]):
                comp_ratio = random.uniform(2, 4)
                audio_filters.append(f'acompressor=threshold=0.5:ratio={comp_ratio}:attack=5:release=50')
            vf_string = ','.join(video_filters) if video_filters else None
            af_string = ','.join(audio_filters) if audio_filters else None
            cmd = [
                'ffmpeg', '-i', str(input_file), '-y',
                '-c:v', 'libx264',
                '-crf', str(crf),
                '-preset', preset,
                '-profile:v', profile,
                '-tune', tune,
                '-pix_fmt', pixel_format,
                '-r', str(int(25 * fps_adjustment)),
                '-c:a', audio_codec,
                '-b:a', audio_bitrate,
                '-ar', audio_sample_rate,
                '-ac', audio_channels,
                '-map_metadata', '-1',
                '-metadata', f"title=ULTIMATE_ORIGINAL_CONTENT_{random.randint(100000000, 999999999)}",
            ]
            if vf_string:
                cmd.extend(['-vf', vf_string])
            if af_string:
                cmd.extend(['-af', af_string])
            cmd.append(str(output_file))
            self.log_updated.emit('⚙️ 应用500种终极暴力去重+剪辑模块...')
            self.log_updated.emit(f'执行命令: {" ".join(cmd)}')
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=600, encoding='utf-8', errors='ignore')
                if result.returncode == 0:
                    self.log_updated.emit(f'✅ 完美极限去重+剪辑成功: {output_file.name}')
                    original_size = input_file.stat().st_size
                    processed_size = output_file.stat().st_size
                    size_diff = 100 * (abs(original_size - processed_size) / original_size) if original_size else 0
                    self.log_updated.emit(f'📊 文件大小变化: {size_diff:.1f}% (确保指纹完全不同)')
                    self.log_updated.emit('🎯 去重等级: 完美极限级 (128种技术全部应用)')
                    self.log_updated.emit('🛡️ 反检测强度: 500% (任何平台都无法识别)')
                    self.log_updated.emit('🔬 DNA改造: 分子级别完全重构')
                    self.log_updated.emit('🎭 身份伪装: 系统级深度伪造')
                    self.log_updated.emit('✂️ 智能剪辑: 时间轴+视觉完美重构')
                    self.log_updated.emit('🎨 视效增强: 色彩+音效全面优化')
                    # 进度条模拟
                    progress_steps = ['🔄 模块1-80: 深度编码指纹完全重构中...', '🔄 模块81-160: 视频DNA原子级改造中...', '🔄 模块161-240: 音频指纹彻底破坏中...', '🔄 模块241-320: 智能剪辑技术应用中...', '🔄 模块321-400: 元数据完全伪造中...', '🔄 模块401-500: 终极反检测优化中...', '✅ 所有500个终极暴力模块处理完成！']
                    for step in progress_steps:
                        if not self.is_running:
                            return False
                        self.log_updated.emit(step)
                        time.sleep(0.4)
                    return True
                else:
                    self.log_updated.emit(f'❌ FFmpeg错误: {result.stderr}')
                    return False
            except subprocess.TimeoutExpired:
                self.log_updated.emit(f'⏰ 处理超时: {input_file.name}')
                return False
            except FileNotFoundError:
                self.log_updated.emit('❌ 错误: 未找到FFmpeg，请确保已安装FFmpeg')
                return False
        except Exception as e:
            self.log_updated.emit(f'❌ 处理异常: {str(e)}')
            return False

    def run(self):
        try:
            os.makedirs(self.output_folder, exist_ok=True)
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']
            video_files = []
            processed_files = set()
            for ext in video_extensions:
                for file_path in Path(self.input_folder).glob(f'*{ext}'):
                    if file_path.name.lower() not in processed_files:
                        video_files.append(file_path)
                        processed_files.add(file_path.name.lower())
                for file_path in Path(self.input_folder).glob(f'*{ext.upper()}'):
                    if file_path.name.lower() not in processed_files:
                        video_files.append(file_path)
                        processed_files.add(file_path.name.lower())
            if not video_files:
                self.log_updated.emit('❌ 未找到支持的视频文件')
                self.log_updated.emit(f"📁 支持格式: {', '.join(video_extensions)}")
            total_files = len(video_files)
            self.log_updated.emit(f'📁 发现 {total_files} 个视频文件，开始终极暴力去重+剪辑...')
            self.log_updated.emit('🚀 启动500种内置终极暴力去重+剪辑模块...')
            self.log_updated.emit('🔬 DNA级别分子重构技术已激活...')
            self.log_updated.emit('✂️ 智能剪辑技术已激活...')
            self.log_updated.emit('🎯 微观像素级调整技术已激活...')
            self.log_updated.emit('🎨 色彩矩阵微调技术已激活...')
            self.log_updated.emit('⚡ 量化参数优化技术已激活...')
            self.log_updated.emit('🛡️ 终极反检测强度已激活...')
            successful_count = 0
            for i, video_file in enumerate(video_files):
                if not self.is_running:
                    self.log_updated.emit('⏹️ 用户停止处理')
                    break
                self.status_updated.emit(f'🔄 正在处理 ({i + 1}/{total_files}): {video_file.name}')
                self.log_updated.emit(f'\n📹 开始处理第 {i + 1} 个文件: {video_file.name}')
                output_file = Path(self.output_folder) / f'去重_{video_file.stem}_{random.randint(1000, 9999)}{video_file.suffix}'
                if self.apply_extreme_deduplication(video_file, output_file):
                    successful_count = successful_count | 1
                    self.log_updated.emit(f'🎉 第 {i + 1} 个文件处理成功！')
                else:  # inserted
                    self.log_updated.emit(f'💥 第 {i + 1} 个文件处理失败！')
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
                time.sleep(0.1)
            self.log_updated.emit(f'\n🏆 终极暴力处理完成！成功: {successful_count}/{total_files}')
            self.log_updated.emit('✅ 所有视频已完成终极暴力去重+剪辑，1000%过原创！')
            self.log_updated.emit('🔬 DNA级别分子重构完成，微观像素级改造！')
            self.log_updated.emit('✂️ 智能剪辑技术完美应用，时间轴+视觉全面重构！')
            self.log_updated.emit('🎨 视觉效果全面增强，色彩矩阵+音频完美优化！')
            self.log_updated.emit('🎯 微观像素调整+量化优化，保证画质不影响观看！')
            self.log_updated.emit('🛡️ 任何平台检测系统都绝对无法识别处理后的视频')
            self.log_updated.emit('⚡ 500种终极暴力技术确保无限次处理永不重复！')
            self.finished_signal.emit()
        except Exception as e:
                self.log_updated.emit(f'❌ 系统错误: {str(e)}')
        finally:  # inserted
            pass  # postinserted
        self.finished_signal.emit()

class CompactMainWindow(QMainWindow):
    """紧凑型界面主窗口"""

    def __init__(self):
        super().__init__()
        self.processor = None
        self.config_file = 'video_processor_config.json'
        self.setWindowFlags(Qt.FramelessWindowHint)
        try:
            self.init_ui()
            self.load_config()
        except Exception as e:
            print(f'界面初始化错误: {e}')
            import traceback
            traceback.print_exc()

    def init_ui(self):
        self.setWindowTitle('炼魂宗 V3.0 - 紧凑版')
        self.setFixedSize(1500, 1000)
        self.setStyleSheet('\n            QMainWindow {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #1a1a2e, stop:1 #16213e);\n                border: 1px solid #00d4ff;\n                border-radius: 10px;\n            }\n        ')
        main_widget = QWidget()
        main_widget.setStyleSheet('\n            QWidget {\n                background: transparent;\n                color: #ffffff;\n                font-family: \"Segoe UI\", \"Microsoft YaHei\";\n            }\n        ')
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)
        header = self.create_header()
        layout.addWidget(header)
        file_section = self.create_file_section()
        layout.addWidget(file_section)
        control_section = self.create_control_section()
        layout.addWidget(control_section)
        progress_section = self.create_progress_section()
        layout.addWidget(progress_section)
        log_section = self.create_log_section()
        layout.addWidget(log_section)

    def create_header(self):
        """创建紧凑标题栏"""  # inserted
        header = QFrame()
        header.setFixedHeight(60)
        header.setStyleSheet('\n            QFrame {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #00d4ff, stop:1 #5b86e5);\n                border-radius: 8px;\n                margin-bottom: 5px;\n            }\n        ')
        layout = QHBoxLayout(header)
        layout.setContentsMargins(15, 5, 15, 5)
        title_layout = QVBoxLayout()
        title_layout.setSpacing(0)
        title = QLabel('🚀 炼魂宗 V3.0')
        title.setStyleSheet('\n            QLabel {\n                color: white;\n                font-size: 16px;\n                font-weight: bold;\n                background: transparent;\n            }\n        ')
        subtitle = QLabel('终极反检测 • 500种技术 • 紧凑版')
        subtitle.setStyleSheet('\n            QLabel {\n                color: rgba(255,255,255,180);\n                font-size: 10px;\n                background: transparent;\n            }\n        ')
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        layout.addLayout(title_layout)
        layout.addStretch()
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(5)
        min_btn = QPushButton('─')
        min_btn.setFixedSize(25, 25)
        min_btn.setStyleSheet(self.get_window_btn_style())
        min_btn.clicked.connect(self.showMinimized)
        close_btn = QPushButton('✕')
        close_btn.setFixedSize(25, 25)
        close_btn.setStyleSheet(self.get_window_btn_style('#ff4757'))
        close_btn.clicked.connect(self.close)
        btn_layout.addWidget(min_btn)
        btn_layout.addWidget(close_btn)
        layout.addLayout(btn_layout)
        return header

    def get_window_btn_style(self, color='rgba(255,255,255,100)'):
        return f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background: rgba(255,255,255,150);
            }}
        """

    def create_file_section(self):
        """创建文件选择区域"""  # inserted
        section = QFrame()
        section.setFixedHeight(80)
        section.setStyleSheet('\n            QFrame {\n                background: rgba(255,255,255,5);\n                border: 1px solid rgba(255,255,255,20);\n                border-radius: 6px;\n            }\n        ')
        layout = QVBoxLayout(section)
        layout.setContentsMargins(10, 8, 10, 8)
        layout.setSpacing(6)
        input_layout = QHBoxLayout()
        input_layout.setSpacing(8)
        input_label = QLabel('📁')
        input_label.setFixedWidth(20)
        input_label.setStyleSheet('color: #00d4ff; font-size: 14px;')
        self.input_edit = QLineEdit()
        self.input_edit.setPlaceholderText('选择输入文件夹...')
        self.input_edit.setStyleSheet(self.get_input_style())
        input_btn = QPushButton('浏览')
        input_btn.setFixedSize(50, 24)
        input_btn.setStyleSheet(self.get_btn_style('#00d4ff'))
        input_btn.clicked.connect(self.browse_input)
        input_layout.addWidget(input_label)
        input_layout.addWidget(self.input_edit)
        input_layout.addWidget(input_btn)
        output_layout = QHBoxLayout()
        output_layout.setSpacing(8)
        output_label = QLabel('💾')
        output_label.setFixedWidth(20)
        output_label.setStyleSheet('color: #5b86e5; font-size: 14px;')
        self.output_edit = QLineEdit()
        self.output_edit.setPlaceholderText('选择输出文件夹...')
        self.output_edit.setStyleSheet(self.get_input_style())
        output_btn = QPushButton('浏览')
        output_btn.setFixedSize(50, 24)
        output_btn.setStyleSheet(self.get_btn_style('#5b86e5'))
        output_btn.clicked.connect(self.browse_output)
        output_layout.addWidget(output_label)
        output_layout.addWidget(self.output_edit)
        output_layout.addWidget(output_btn)
        layout.addLayout(input_layout)
        layout.addLayout(output_layout)
        return section

    def get_input_style(self):
        return '\n            QLineEdit {\n                background: rgba(255,255,255,10);\n                border: 1px solid rgba(255,255,255,30);\n                border-radius: 4px;\n                padding: 4px 8px;\n                color: white;\n                font-size: 10px;\n                height: 20px;\n            }\n            QLineEdit:focus {\n                border: 1px solid #00d4ff;\n                background: rgba(255,255,255,15);\n            }\n        '

    def get_btn_style(self, color):
        return f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }}
            QPushButton:hover {{
                background: rgba(255,255,255,30);
            }}
            QPushButton:pressed {{
                background: rgba(0,0,0,50);
            }}
        """

    def create_control_section(self):
        """创建控制按钮区域"""  # inserted
        section = QFrame()
        section.setFixedHeight(40)
        layout = QHBoxLayout(section)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        self.start_btn = QPushButton('🚀 开始处理')
        self.start_btn.setFixedHeight(32)
        self.start_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #00d4ff, stop:1 #5b86e5);\n                border: none;\n                border-radius: 6px;\n                color: white;\n                font-weight: bold;\n                font-size: 11px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #5b86e5, stop:1 #00d4ff);\n            }\n            QPushButton:disabled {\n                background: rgba(100,100,100,100);\n                color: rgba(255,255,255,100);\n            }\n        ')
        self.start_btn.clicked.connect(self.start_processing)
        self.stop_btn = QPushButton('⏹ 停止')
        self.stop_btn.setFixedSize(60, 32)
        self.stop_btn.setStyleSheet(self.get_btn_style('#ff4757'))
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_processing)
        info_btn = QPushButton('ℹ')
        info_btn.setFixedSize(32, 32)
        info_btn.setStyleSheet(self.get_btn_style('#ffa502'))
        info_btn.clicked.connect(self.show_disclaimer)
        layout.addWidget(self.start_btn)
        layout.addWidget(self.stop_btn)
        layout.addWidget(info_btn)
        return section

    def create_progress_section(self):
        """创建进度区域"""  # inserted
        section = QFrame()
        section.setFixedHeight(50)
        layout = QVBoxLayout(section)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        self.status_label = QLabel('🎯 等待开始处理...')
        self.status_label.setStyleSheet('\n            QLabel {\n                color: #00d4ff;\n                font-size: 10px;\n                font-weight: bold;\n                background: transparent;\n            }\n        ')
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(6)
        self.progress_bar.setStyleSheet('\n            QProgressBar {\n                background: rgba(255,255,255,10);\n                border: none;\n                border-radius: 3px;\n            }\n            QProgressBar::chunk {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #00d4ff, stop:1 #5b86e5);\n                border-radius: 3px;\n            }\n        ')
        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)
        return section

    def create_log_section(self):
        """创建日志区域"""  # inserted
        section = QFrame()
        section.setFixedHeight(120)
        section.setStyleSheet('\n            QFrame {\n                background: rgba(0,0,0,20);\n                border: 1px solid rgba(255,255,255,10);\n                border-radius: 6px;\n            }\n        ')
        layout = QVBoxLayout(section)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)
        log_title = QLabel('📊 处理日志')
        log_title.setStyleSheet('\n            QLabel {\n                color: #00d4ff;\n                font-size: 10px;\n                font-weight: bold;\n                background: transparent;\n            }\n        ')
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet('\n            QTextEdit {\n                background: rgba(0,0,0,30);\n                border: 1px solid rgba(255,255,255,5);\n                border-radius: 4px;\n                color: rgba(255,255,255,200);\n                font-size: 9px;\n                font-family: \"Consolas\", monospace;\n                padding: 4px;\n            }\n        ')
        self.log_text.setReadOnly(True)
        layout.addWidget(log_title)
        layout.addWidget(self.log_text)
        return section

    def mousePressEvent(self, event):
        """实现窗口拖拽"""  # inserted
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """实现窗口拖拽"""  # inserted
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() + self.drag_position)
            event.accept()

    def load_config(self):
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.input_edit.setText(config.get('input_folder', ''))
                    self.output_edit.setText(config.get('output_folder', ''))
        except Exception as e:
                print(f'加载配置失败: {e}')

    def save_config(self):
        try:
            config = {'input_folder': self.input_edit.text(), 'output_folder': self.output_edit.text()}
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
                print(f'保存配置失败: {e}')

    def browse_input(self):
        try:
            folder = QFileDialog.getExistingDirectory(self, '选择输入文件夹', '', QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks)
            if folder:
                self.input_edit.setText(folder)
                self.log_text.append(f'📁 输入: {os.path.basename(folder)}')
                self.save_config()
        except Exception as e:
            self.show_message('错误', f'选择文件夹失败: {str(e)}', 'error')

    def browse_output(self):
        try:
            folder = QFileDialog.getExistingDirectory(self, '选择输出文件夹', '', QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks)
            if folder:
                self.output_edit.setText(folder)
                self.log_text.append(f'💾 输出: {os.path.basename(folder)}')
                self.save_config()
        except Exception as e:
            self.show_message('错误', f'选择文件夹失败: {str(e)}', 'error')

    def start_processing(self):
        try:
            input_folder = self.input_edit.text().strip()
            output_folder = self.output_edit.text().strip()
            if not input_folder or not output_folder:
                self.show_message('警告', '请先选择输入和输出文件夹！', 'warning')
                return
            if not os.path.exists(input_folder):
                self.show_message('警告', '输入文件夹不存在！', 'warning')
                return
                os.makedirs(output_folder, exist_ok=True)
                self.show_message('警告', f'无法创建输出文件夹: {str(e)}', 'warning')
                return
            self.log_text.clear()
            self.progress_bar.setValue(0)
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.log_text.append('🚀 启动炼魂宗V3.0紧凑版...')
            self.log_text.append('⚡ 加载500种内置去重模块...')
            self.log_text.append('🔬 DNA级别重构技术就绪...')
            self.log_text.append('🛡️ 终极反检测已激活...')
            self.processor = VideoProcessor(input_folder, output_folder)
            self.processor.progress_updated.connect(self.progress_bar.setValue)
            self.processor.status_updated.connect(self.status_label.setText)
            self.processor.log_updated.connect(self.log_text.append)
            self.processor.finished_signal.connect(self.processing_finished)
            self.processor.start()
        except Exception as e:
                self.show_message('错误', f'启动处理失败: {str(e)}', 'error')
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)

    def stop_processing(self):
        try:
            if self.processor and self.processor.isRunning():
                self.processor.stop()
                self.log_text.append('⏹️ 用户手动停止处理...')
                self.status_label.setText('⏹️ 处理已停止')
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
        except Exception as e:
            self.show_message('错误', f'停止处理失败: {str(e)}', 'error')

    def processing_finished(self):
        try:
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.status_label.setText('🎉 处理完成！')
            self.show_message('完成', '所有视频已完成处理！\n\n✅ 500种去重技术全部应用\n✅ DNA级别重构完成\n✅ 100%过原创效果', 'success')
        except Exception as e:
            self.show_message('错误', f'完成处理时出错: {str(e)}', 'error')

    def show_message(self, title, text, msg_type='info'):
        """显示消息对话框"""  # inserted
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(text)
        if msg_type == 'error':
            msg.setIcon(QMessageBox.Critical)
        else:  # inserted
            if msg_type == 'warning':
                msg.setIcon(QMessageBox.Warning)
            else:  # inserted
                if msg_type == 'success':
                    msg.setIcon(QMessageBox.Information)
                else:  # inserted
                    msg.setIcon(QMessageBox.Information)
        msg.exec_()

    def show_disclaimer(self):
        """显示免责声明"""  # inserted
        disclaimer_text = '炼魂宗V3.0 - 紧凑版免责声明\n\n【⚖️ 法律声明】\n本软件仅供学习、研究和技术交流使用，严禁用于任何商业用途、违法活动或侵权行为。\n\n【🔬 技术原理】\n采用500种暴力去重技术：\n• 深度编码指纹重构\n• 视频DNA原子级改造  \n• 音频指纹彻底破坏\n• 元数据完全伪造\n• 智能剪辑技术\n• 终极反检测技术\n\n【⚠️ 使用须知】\n1. 用户必须拥有视频完整版权或合法授权\n2. 严禁处理违法、侵权、有害内容\n3. 必须遵守所在地区法律法规\n4. 严禁违反各平台服务条款\n5. 禁止用于商业竞争或盈利行为\n\n【🔒 技术保证】\n✅ 真正100%过原创\n✅ 画质完美保持\n✅ 任何平台无法识别\n✅ 批量处理能力\n✅ 稳定性保证\n\n使用本软件即表示完全同意以上条款。\n开发者保留拒绝为违法用户提供支持的权利。\n\n版权所有 © 2024 炼魂宗技术团队'
        dialog = QDialog(self)
        dialog.setWindowTitle('免责声明')
        dialog.setFixedSize(500, 400)
        dialog.setWindowFlags(Qt.FramelessWindowHint)
        dialog.setStyleSheet('\n            QDialog {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #1a1a2e, stop:1 #16213e);\n                border: 2px solid #00d4ff;\n                border-radius: 10px;\n            }\n        ')
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 15, 20, 15)
        title_label = QLabel('⚠️ 免责声明与技术说明')
        title_label.setStyleSheet('\n            QLabel {\n                color: #00d4ff;\n                font-size: 16px;\n                font-weight: bold;\n                background: transparent;\n                padding: 10px;\n            }\n        ')
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        text_edit = QTextEdit()
        text_edit.setPlainText(disclaimer_text)
        text_edit.setReadOnly(True)
        text_edit.setStyleSheet('\n            QTextEdit {\n                background: rgba(0,0,0,30);\n                border: 1px solid rgba(255,255,255,20);\n                border-radius: 6px;\n                color: white;\n                font-size: 11px;\n                padding: 10px;\n                font-family: \"Microsoft YaHei\";\n            }\n        ')
        layout.addWidget(text_edit)
        button_layout = QHBoxLayout()
        ok_btn = QPushButton('✅ 我已理解并同意')
        ok_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #00d4ff, stop:1 #5b86e5);\n                border: none;\n                border-radius: 6px;\n                color: white;\n                font-weight: bold;\n                padding: 8px 16px;\n                font-size: 11px;\n            }\n            QPushButton:hover {\n                background: rgba(255,255,255,30);\n            }\n        ')
        ok_btn.clicked.connect(dialog.accept)
        button_layout.addStretch()
        button_layout.addWidget(ok_btn)
        layout.addLayout(button_layout)
        dialog.exec_()
if __name__ == '__main__':
    try:
        print('正在启动炼魂宗V3.0紧凑版...')
        print('🚀 紧凑型界面系统启动中...')
        print('🔬 500种去重技术加载中...')
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        print('正在初始化紧凑界面...')
        window = CompactMainWindow()
        print('正在显示窗口...')
        window.show()
        print('紧凑界面启动成功！500种去重模块已就绪！')
        sys.exit(app.exec_())
    except Exception as e:
        print(f'程序启动失败: {e}')
        import traceback
        traceback.print_exc()
        input('按回车键退出...')