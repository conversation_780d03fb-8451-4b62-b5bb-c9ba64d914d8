import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import subprocess
import threading
import queue
import tempfile
import os
import sys
from datetime import datetime
import random
import string

class 末世AB(tk.Tk):
    def toggle_batch_mode(self):
        self.video_a_entry.delete(0, "end")
        self.video_b_entry.delete(0, "end")
        is_batch = self.batch_mode_var.get()
        self.log(f"--- 模式切换 --- 批量模式: {'启用' if is_batch else '关闭'}")
        if is_batch:
            self.video_b_label.config(text="主视频文件夹:")
            self.video_a_label.config(text="副视频文件夹:")
            self.process_button.config(text="开始批量处理")
        else:
            self.video_b_label.config(text="主视频:")
            self.video_a_label.config(text="副视频:")
            self.process_button.config(text="开始处理")

    def browse_video_a_file(self):
        self.log("正在调用 [副视频文件] 选择窗口...")
        path = filedialog.askopenfilename(title="选择副视频文件", filetypes=[("视频文件", "*.mp4 *.mov *.avi *.mkv"), ("所有文件", "*.*")])
        if path:
            self.video_a_entry.delete(0, "end")
            self.video_a_entry.insert(0, path)
            self.log(f"已选择副视频文件: {path}")

    def browse_video_a_dir(self):
        self.log("正在调用 [副视频文件夹] 选择窗口...")
        path = filedialog.askdirectory(title="选择副视频文件夹")
        if path:
            self.video_a_entry.delete(0, "end")
            self.video_a_entry.insert(0, path)
            self.log(f"已选择副视频文件夹: {path}")

    def browse_video_b_file(self):
        self.log("正在调用 [主视频文件] 选择窗口...")
        path = filedialog.askopenfilename(title="选择主视频文件", filetypes=[("视频文件", "*.mp4 *.mov *.avi *.mkv"), ("所有文件", "*.*")])
        if path:
            self.video_b_entry.delete(0, "end")
            self.video_b_entry.insert(0, path)
            self.log(f"已选择主视频文件: {path}")

    def browse_video_b_dir(self):
        self.log("正在调用 [主视频文件夹] 选择窗口...")
        path = filedialog.askdirectory(title="选择主视频文件夹")
        if path:
            self.video_b_entry.delete(0, "end")
            self.video_b_entry.insert(0, path)
            self.log(f"已选择主视频文件夹: {path}")

    def browse_output_dir(self):
        path = filedialog.askdirectory(title="选择输出目录")
        if path:
            self.output_entry.delete(0, "end")
            self.output_entry.insert(0, path)

    def __init__(self):
        super().__init__()
        self.title("末世AB - 闪烁交错AB工具")
        self.geometry("800x600")
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill="both", expand=True)
        main_frame.grid_columnconfigure(1, weight=1)
        self.batch_mode_var = tk.BooleanVar()
        self.batch_mode_check = ttk.Checkbutton(
            main_frame,
            text="启用文件夹批量模式",
            variable=self.batch_mode_var,
            command=self.toggle_batch_mode
        )
        self.batch_mode_check.grid(row=0, column=1, pady=(0, 10), sticky='w')
        self.video_b_label = ttk.Label(main_frame, text="主视频:")
        self.video_b_label.grid(row=1, column=0, padx=(0, 5), pady=5, sticky="w")
        self.video_b_entry = ttk.Entry(main_frame)
        self.video_b_entry.grid(row=1, column=1, sticky="ew")
        self.video_b_browse_button = ttk.Button(main_frame, text="浏览...", command=self.browse_video_b_file)
        self.video_b_browse_button.grid(row=1, column=2, padx=5)
        self.video_a_label = ttk.Label(main_frame, text="副视频:")
        self.video_a_label.grid(row=2, column=0, padx=(0, 5), pady=5, sticky="w")
        self.video_a_entry = ttk.Entry(main_frame)
        self.video_a_entry.grid(row=2, column=1, sticky="ew")
        self.video_a_browse_button = ttk.Button(main_frame, text="浏览...", command=self.browse_video_a_file)
        self.video_a_browse_button.grid(row=2, column=2, padx=5)
        self.output_label = ttk.Label(main_frame, text="输出文件夹:")
        self.output_label.grid(row=3, column=0, padx=(0, 5), pady=5, sticky="w")
        self.output_entry = ttk.Entry(main_frame)
        self.output_entry.grid(row=3, column=1, sticky="ew")
        self.output_browse_button = ttk.Button(main_frame, text="浏览...", command=self.browse_output_dir)
        self.output_browse_button.grid(row=3, column=2, padx=5)
        self.process_button = ttk.Button(main_frame, text="开始处理", command=self.start_processing)
        self.process_button.grid(row=4, column=1, pady=10)
        log_frame = ttk.LabelFrame(main_frame, text="处理日志")
        log_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0), sticky="nsew")
        main_frame.grid_rowconfigure(5, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)
        self.log_textbox = tk.Text(log_frame, state="disabled", wrap="word", height=15)
        self.log_textbox.grid(row=0, column=0, sticky="nsew")
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_textbox.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.log_textbox['yscrollcommand'] = scrollbar.set
        self.log_queue = queue.Queue()
        self.after(100, self.process_log_queue)
        self.ffmpeg_path, self.ffprobe_path = self.get_ffmpeg_paths()

    def log(self, message):
        self.log_textbox.configure(state="normal")
        self.log_textbox.insert("end", message + "\n")
        self.log_textbox.configure(state="disabled")
        self.log_textbox.see("end")

    def process_log_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                if message == "PROCESS_FINISHED":
                    self.set_ui_state("normal")
                    messagebox.showinfo("完成", "所有视频已处理完毕！")
                elif message == "PROCESS_FAILED":
                    self.set_ui_state("normal")
                    messagebox.showerror("失败", "处理过程中发生错误，请查看日志。")
                else:
                    self.log(message)
        except queue.Empty:
            pass
        finally:
            self.after(100, self.process_log_queue)

    def start_processing(self):
        try:
            video_a_path = self.video_a_entry.get()
            video_b_path = self.video_b_entry.get()
            output_dir_path = self.output_entry.get()
            if not all([video_a_path, video_b_path, output_dir_path]):
                messagebox.showwarning("警告", "请填写主视频、副视频和输出路径。")
                return
            if not os.path.exists(self.ffmpeg_path) or not os.path.exists(self.ffprobe_path):
                messagebox.showerror("错误", "FFmpeg组件未找到。")
                return
            if not os.path.isdir(output_dir_path):
                messagebox.showerror("错误", f"输出文件夹路径无效:\n{output_dir_path}")
                return
            video_a_list = []
            video_b_list = []
            if self.batch_mode_var.get():
                if not os.path.isdir(video_a_path):
                    messagebox.showerror("错误", f"副视频文件夹路径无效:\n{video_a_path}")
                    return
                if not os.path.isdir(video_b_path):
                    messagebox.showerror("错误", f"主视频文件夹路径无效:\n{video_b_path}")
                    return
                video_extensions = ('.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv')
                video_a_list = sorted([os.path.join(video_a_path, f) for f in os.listdir(video_a_path) if f.lower().endswith(video_extensions)])
                video_b_list = sorted([os.path.join(video_b_path, f) for f in os.listdir(video_b_path) if f.lower().endswith(video_extensions)])
                if not video_a_list or not video_b_list:
                    messagebox.showerror("错误", "主/副视频文件夹中未找到有效视频文件。")
                    return
                if len(video_a_list) != len(video_b_list):
                    messagebox.showwarning("警告", "主/副视频数量不一致，将按最短列表配对处理。")
                min_len = min(len(video_a_list), len(video_b_list))
                video_a_list = video_a_list[:min_len]
                video_b_list = video_b_list[:min_len]
            else:
                video_a_list = [video_a_path]
                video_b_list = [video_b_path]
            self.set_ui_state("disabled")
            threading.Thread(target=self.run_processing_logic, args=(video_a_list, video_b_list, output_dir_path, self.batch_mode_var.get()), daemon=True).start()
        except Exception as e:
            self.log_queue.put(f"❌ 启动处理失败: {e}")
            self.set_ui_state("normal")

    def set_ui_state(self, state):
        widgets = [
            self.video_a_entry, self.video_b_entry, self.output_entry,
            self.video_a_browse_button, self.video_b_browse_button, self.output_browse_button,
            self.process_button, self.batch_mode_check
        ]
        for w in widgets:
            w.config(state=state)

    def get_ffmpeg_paths(self):
        # 兼容打包和源码，始终用exe同级目录
        if hasattr(sys, '_MEIPASS'):
            exe_dir = os.path.dirname(sys.executable)
        else:
            exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        ffmpeg_dir = os.path.join(exe_dir, 'ffmpeg', 'bin')
        ffmpeg_path = os.path.join(ffmpeg_dir, 'ffmpeg.exe')
        ffprobe_path = os.path.join(ffmpeg_dir, 'ffprobe.exe')
        return ffmpeg_path, ffprobe_path

    def get_random_ffmpeg_path(self):
        # 兼容打包和源码，始终用exe同级目录
        if hasattr(sys, '_MEIPASS'):
            exe_dir = os.path.dirname(sys.executable)
        else:
            exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
        bin_dir = os.path.join(exe_dir, 'ffmpeg', 'bin')
        src = os.path.join(bin_dir, 'ffmpeg.exe')
        rand_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8)) + '.exe'
        dst = os.path.join(bin_dir, rand_name)
        import shutil
        shutil.copy(src, dst)
        return dst

    def cleanup_random_ffmpeg(self, path):
        try:
            if os.path.exists(path):
                os.remove(path)
        except Exception:
            pass

    def get_video_duration(self, video_path):
        cmd = [self.ffprobe_path, '-v', 'error', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path]
        try:
            out = subprocess.check_output(cmd, stderr=subprocess.STDOUT, creationflags=subprocess.CREATE_NO_WINDOW)
            return float(out.decode().strip())
        except Exception as e:
            self.log_queue.put(f"⚠️ 获取视频时长失败: {e}")
            return None

    def get_video_resolution(self, video_path):
        cmd = [self.ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'csv=p=0', video_path]
        try:
            out = subprocess.check_output(cmd, stderr=subprocess.STDOUT, creationflags=subprocess.CREATE_NO_WINDOW)
            w, h = out.decode().strip().split(',')
            return int(w), int(h)
        except Exception as e:
            self.log_queue.put(f"⚠️ 获取视频分辨率失败: {e}")
            return 1080, 2106

    def run_processing_logic(self, video_a_list, video_b_list, output_dir_path, is_batch, del_a=False, del_b=False):
        self.log_queue.put("末世AB处理中请等待")
        try:
            total = len(video_a_list)
            for idx, (a, b) in enumerate(zip(video_a_list, video_b_list), 1):
                try:
                    self.process_single_video_pair(idx, total, a, b, output_dir_path, del_a=del_a, del_b=del_b)
                except Exception as e:
                    self.log_queue.put("末世AB处理完成")
                    self.log_queue.put("PROCESS_FAILED")
                    return
            self.log_queue.put("末世AB处理完成")
            self.log_queue.put("PROCESS_FINISHED")
        except Exception as e:
            self.log_queue.put("末世AB处理完成")
            self.log_queue.put("PROCESS_FAILED")

    def process_single_video_pair(self, current_num, total_num, video_a_path, video_b_path, output_dir_path, del_a=False, del_b=False):
        try:
            # 不输出任何日志
            input_basename = os.path.basename(video_b_path)
            input_name = os.path.splitext(input_basename)[0]
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            final_output_path = os.path.join(output_dir_path, f"{timestamp}_末世AB_{input_name}.mp4")
            with tempfile.TemporaryDirectory() as temp_dir:
                duration = self.get_video_duration(video_b_path)
                if duration is None:
                    duration = 10.0
                duration_str = f"{duration:.6f}"
                width, height = self.get_video_resolution(video_b_path)
                ffmpeg1 = self.get_random_ffmpeg_path()
                left_1 = os.path.join(temp_dir, "left_1.mp4")
                cmd2 = [ffmpeg1, '-y', '-i', video_b_path, '-vf', 'fps=60', '-c:v', 'libx264', '-preset', 'medium', '-x264-params', 'crf=23', '-profile:v', 'main', '-pix_fmt', 'yuv420p', '-movflags', '+faststart', '-flags', '+cgop', '-tune', 'ssim', '-c:a', 'copy', left_1]
                subprocess.run(cmd2, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
                self.cleanup_random_ffmpeg(ffmpeg1)
                ffmpeg2 = self.get_random_ffmpeg_path()
                left_2 = os.path.join(temp_dir, "left_2.mp4")
                vf3 = f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60'
                cmd3 = [ffmpeg2, '-y', '-i', left_1, '-ss', '00:00:00', '-t', '0.333', '-vf', vf3, '-c:v', 'libx264', '-preset', 'medium', '-x264-params', 'crf=23', '-profile:v', 'main', '-pix_fmt', 'yuv420p', '-movflags', '+faststart', '-flags', '+cgop', '-tune', 'ssim', '-an', left_2]
                subprocess.run(cmd3, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
                self.cleanup_random_ffmpeg(ffmpeg2)
                ffmpeg3 = self.get_random_ffmpeg_path()
                right_1 = os.path.join(temp_dir, "right_1.mp4")
                vf4 = f'scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1:1,fps=60'
                cmd4 = [ffmpeg3, '-y', '-stream_loop', '-1', '-i', video_a_path, '-map_metadata', '-1', '-t', duration_str, '-vf', vf4, '-c:v', 'libx264', '-preset', 'medium', '-x264-params', 'crf=23', '-profile:v', 'main', '-pix_fmt', 'yuv420p', '-movflags', '+faststart', '-flags', '+cgop', '-tune', 'ssim', '-an', right_1]
                subprocess.run(cmd4, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
                self.cleanup_random_ffmpeg(ffmpeg3)
                ffmpeg4 = self.get_random_ffmpeg_path()
                left_3 = os.path.join(temp_dir, "left_3.mp4")
                cmd5 = [ffmpeg4, '-y', '-i', left_2, '-i', right_1, '-filter_complex', f'[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration_str}', '-c:v', 'libx264', '-preset', 'medium', '-x264-params', 'crf=23', '-profile:v', 'main', '-pix_fmt', 'yuv420p', '-movflags', '+faststart', '-flags', '+cgop', '-tune', 'ssim', '-an', left_3]
                subprocess.run(cmd5, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
                self.cleanup_random_ffmpeg(ffmpeg4)
                ffmpeg5 = self.get_random_ffmpeg_path()
                cmd6 = [ffmpeg5, '-y', '-i', left_1, '-i', left_3, '-filter_complex', "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]", '-map', '[v]', '-c:v', 'libx264', '-preset', 'medium', '-x264-params', 'crf=23', '-profile:v', 'main', '-pix_fmt', 'yuv420p', '-movflags', '+faststart', '-flags', '+cgop', '-tune', 'ssim', '-vsync', 'vfr', '-video_track_timescale', '1000000', '-map', '0:a?', '-c:a', 'copy', '-t', duration_str, final_output_path]
                subprocess.run(cmd6, stdout=subprocess.PIPE, stderr=subprocess.PIPE, creationflags=subprocess.CREATE_NO_WINDOW)
                self.cleanup_random_ffmpeg(ffmpeg5)
            if del_a:
                try:
                    os.remove(video_a_path)
                except Exception as e:
                    pass
            if del_b:
                try:
                    os.remove(video_b_path)
                except Exception as e:
                    pass
        except Exception as e:
            pass 