import os
import sys
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import queue
import tempfile
import subprocess
from datetime import datetime

def get_ffmpeg_path():
    exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    ffmpeg_path = os.path.join(exe_dir, 'ffmpeg', 'ffmpeg.exe')
    if os.path.exists(ffmpeg_path):
        return ffmpeg_path
    # 尝试其他可能的路径
    ffmpeg_path = os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffmpeg.exe')
    if os.path.exists(ffmpeg_path):
        return ffmpeg_path
    raise FileNotFoundError(f"未找到ffmpeg: {ffmpeg_path}")

def get_ffprobe_path():
    exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    ffprobe_path = os.path.join(exe_dir, 'ffmpeg', 'ffprobe.exe')
    if os.path.exists(ffprobe_path):
        return ffprobe_path
    # 尝试其他可能的路径
    ffprobe_path = os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffprobe.exe')
    if os.path.exists(ffprobe_path):
        return ffprobe_path
    raise FileNotFoundError(f"未找到ffprobe: {ffprobe_path}")

class 飞天AB:
    def __init__(self, log_callback=None, ffmpeg_path=None, ffprobe_path=None):
        self.log_callback = log_callback
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path

    def log(self, msg):
        if self.log_callback:
            self.log_callback(msg)



    def get_video_info(self, video_path):
        ffprobe_path = self.ffprobe_path or get_ffprobe_path()
        # 获取时长
        cmd_dur = [ffprobe_path, '-v', 'quiet', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path]
        duration = float(subprocess.run(cmd_dur, capture_output=True, text=True).stdout.strip())
        return duration

    def _run_ffmpeg_pipeline(self, main_file, aux_file, basename, output_dir):
        """执行完整的FFmpeg处理流程"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 获取主视频时长
        duration = self.get_video_info(main_file)
        fps = 60
        bitrate = 15000
        
        with tempfile.TemporaryDirectory() as temp_dir:
            self.log(f"[1/5] 处理主视频...")
            temp1 = os.path.join(temp_dir, "temp1.mp4")
            cmd1 = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-i", main_file,
                "-vf", f"scale=1080:2106:force_original_aspect_ratio=decrease,pad=1080:2106:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps={fps}",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-b:v", f"{bitrate}k", "-profile:v", "high", "-level", "4.0",
                "-pix_fmt", "yuv420p", "-movflags", "+faststart", "-flags", "+cgop",
                "-aq-strength", "0.8", "-deblock", "1:1", "-tune", "grain",
                "-c:a", "copy", temp1
            ]
            self.run_cmd(cmd1)

            self.log(f"[2/5] 截取主视频片段...")
            temp2 = os.path.join(temp_dir, "temp2.mp4")
            cmd2 = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-i", temp1,
                "-ss", "00:00:00", "-t", "0.333",
                "-vf", f"scale=1080:2106:force_original_aspect_ratio=decrease,pad=1080:2106:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps={fps}",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-b:v", f"{bitrate}k", "-profile:v", "high", "-level", "4.0",
                "-pix_fmt", "yuv420p", "-movflags", "+faststart", "-flags", "+cgop",
                "-init_qpP", "0", "-quality", "best", "-rc", "cbr", temp2
            ]
            self.run_cmd(cmd2)

            self.log(f"[3/5] 处理副视频...")
            temp3 = os.path.join(temp_dir, "temp3.mp4")
            cmd3 = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-stream_loop", "-1", "-i", aux_file,
                "-t", str(duration + 0.016666666666667),
                "-vf", f"scale=1080:2106:force_original_aspect_ratio=increase,crop=1080:2106:(iw-ow)/2:(ih-oh)/2,setsar=1:1,fps={fps}",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                "-deblock", "1:1", "-tune", "grain", "-an", temp3
            ]
            self.run_cmd(cmd3)

            self.log(f"[4/5] 拼接视频片段...")
            temp4 = os.path.join(temp_dir, "temp4.mp4")
            cmd4 = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-i", temp2, "-i", temp3,
                "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                "-deblock", "1:1", "-tune", "grain", temp4
            ]
            self.run_cmd(cmd4)

            self.log(f"[5/5] 生成最终交错视频...")
            out_file = os.path.join(output_dir, f"{basename}_飞天AB_{timestamp}.mp4")
            cmd5 = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-i", temp1, "-i", temp4,
                "-filter_complex", "[0:v]setsar=1,select=not(mod(n\\,2))[a];[1:v]setsar=1,select=mod(n\\,2)[b];[a][b]interleave,select=not(eq(n\\,0))[v]",
                "-map", "[v]", "-c:v", "libx264", "-preset", "faster",
                "-x264-params", f"nal-hrd=cbr:vbv-maxrate={bitrate}:vbv-bufsize={bitrate}",
                "-b:v", f"{bitrate}k", "-profile:v", "high", "-level", "4.2",
                "-pix_fmt", "yuv420p", "-movflags", "+faststart", "-flags", "+cgop",
                "-aq-strength", "0.8", "-deblock", "1:1", "-tune", "grain",
                "-vsync", "vfr", "-rc", "constqp", "-qp", "30", "-bf", "0",
                "-video_track_timescale", "1000000", "-map", "0:a:0", "-c:a", "copy",
                "-t", str(duration), out_file
            ]
            self.run_cmd(cmd5)
            
            # 添加主视频缩略图
            self.log(f"[6/6] 添加主视频缩略图...")
            temp_thumbnail = os.path.join(temp_dir, "thumbnail.jpg")
            
            # 获取主视频的宽高比，生成合适尺寸的缩略图
            # 输出视频是1080x2106，所以缩略图也按这个比例
            thumbnail_cmd = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-i", main_file, "-ss", "00:00:01", "-vframes", "1",
                "-vf", "scale=108:210:force_original_aspect_ratio=decrease,pad=108:210:(ow-iw)/2:(oh-ih)/2", 
                "-q:v", "2", temp_thumbnail
            ]
            self.run_cmd(thumbnail_cmd)
            
            final_out = os.path.join(output_dir, f"{basename}_飞天AB_{timestamp}_final.mp4")
            thumbnail_cmd2 = [
                self.ffmpeg_path or get_ffmpeg_path(), "-y", "-i", out_file, "-i", temp_thumbnail,
                "-map", "0:v", "-map", "0:a", "-map", "1", "-c", "copy",
                "-c:v:1", "mjpeg", "-disposition:v:1", "attached_pic", final_out
            ]
            self.run_cmd(thumbnail_cmd2)
            
            # 删除临时文件，重命名最终文件
            os.remove(out_file)
            os.rename(final_out, out_file)
            
            self.log(f"[处理完成] 输出文件：{out_file}")

    def run_cmd(self, cmd):
        """执行FFmpeg命令"""
        # 创建STARTUPINFO对象来隐藏控制台窗口
        startupinfo = None
        if hasattr(subprocess, 'STARTUPINFO'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
        
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore',
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{' '.join(cmd)}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")

    def run_processing_logic(self, main_list, aux_list, output_dir, is_batch=False, del_a=False, del_b=False):
        """主处理逻辑"""
        try:
            self.log("飞天AB处理中请等待")
            
            if is_batch:
                video_extensions = ('.mp4', '.mkv', '.mov', '.avi')
                main_files = sorted([f for f in os.listdir(main_list[0]) if f.lower().endswith(video_extensions)])
                aux_files = sorted([f for f in os.listdir(aux_list[0]) if f.lower().endswith(video_extensions)])
                
                if not main_files or not aux_files:
                    self.log("主/副视频文件夹中未找到视频文件！")
                    return
                
                min_len = min(len(main_files), len(aux_files))
                for idx in range(min_len):
                    try:
                        main_file = os.path.join(main_list[0], main_files[idx])
                        aux_file = os.path.join(aux_list[0], aux_files[idx])
                        basename = os.path.splitext(main_files[idx])[0]
                        self.log(f"[{idx+1}/{min_len}] 主：{main_files[idx]} 副：{aux_files[idx]}")
                        self._run_ffmpeg_pipeline(main_file, aux_file, basename, output_dir)
                        self.log(f"✔️ 完成：{basename}")
                    except Exception as e:
                        self.log(f"❌ 失败：{main_files[idx]} + {aux_files[idx]}，原因：{e}")
            else:
                basename = os.path.splitext(os.path.basename(main_list[0]))[0]
                self._run_ffmpeg_pipeline(main_list[0], aux_list[0], basename, output_dir)
            
            self.log("飞天AB处理完成")
        except Exception as e:
            self.log(f"出错：{e}")

 