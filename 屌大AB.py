import os
import sys
import threading
import tempfile
import subprocess
from datetime import datetime

def get_ffmpeg_path():
    # 获取程序所在目录
    if getattr(sys, 'frozen', False):
        # 打包后的路径
        exe_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境路径
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    
    # 尝试多个可能的路径
    possible_paths = [
        os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffmpeg.exe'),
        os.path.join(exe_dir, 'ffmpeg', 'ffmpeg.exe'),
        os.path.join(exe_dir, 'ffmpeg.exe'),
        os.path.join(exe_dir, 'bin', 'ffmpeg.exe'),
        os.path.join(exe_dir, 'tools', 'ffmpeg.exe')
    ]
    
    for ffmpeg_path in possible_paths:
        if os.path.exists(ffmpeg_path):
            return ffmpeg_path
    
    raise FileNotFoundError(f"未找到ffmpeg，请确保ffmpeg.exe在程序目录中")

def get_ffprobe_path():
    # 获取程序所在目录
    if getattr(sys, 'frozen', False):
        # 打包后的路径
        exe_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境路径
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    
    # 尝试多个可能的路径
    possible_paths = [
        os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffprobe.exe'),
        os.path.join(exe_dir, 'ffmpeg', 'ffprobe.exe'),
        os.path.join(exe_dir, 'ffprobe.exe'),
        os.path.join(exe_dir, 'bin', 'ffprobe.exe'),
        os.path.join(exe_dir, 'tools', 'ffprobe.exe')
    ]
    
    for ffprobe_path in possible_paths:
        if os.path.exists(ffprobe_path):
            return ffprobe_path
    
    raise FileNotFoundError(f"未找到ffprobe，请确保ffprobe.exe在程序目录中")

class 屌大AB:
    def __init__(self, log_callback=None, ffmpeg_path=None, ffprobe_path=None):
        self.log_callback = log_callback or print
        self.ffmpeg_path = ffmpeg_path or get_ffmpeg_path()
        self.ffprobe_path = ffprobe_path or get_ffprobe_path()

    def log(self, msg):
        if self.log_callback:
            self.log_callback(msg)

    def run_cmd(self, cmd):
        """执行FFmpeg命令"""
        # 创建STARTUPINFO对象来隐藏控制台窗口
        startupinfo = None
        if hasattr(subprocess, 'STARTUPINFO'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
        
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore',
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{' '.join(cmd)}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")

    def process_single_video(self, main_file, background_file, output_dir):
        """处理单个视频"""
        basename = os.path.splitext(os.path.basename(main_file))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.log(f"开始处理：{basename}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 步骤1: 处理主视频
            self.log(f"第一步处理中...")
            a1_file = os.path.join(temp_dir, "a1.mp4")
            cmd1 = [
                self.ffmpeg_path, "-y", "-i", main_file,
                "-vf", "scale=1080:2106:force_original_aspect_ratio=decrease,pad=1080:2106:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-b:v", "15000k", "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                "-deblock", "1:1", "-tune", "grain", "-c:a", "copy", "-threads", "0",
                "-t", "10.005000", a1_file
            ]
            self.run_cmd(cmd1)

            # 步骤2: 提取片段
            self.log(f"第二步处理中...")
            a0_file = os.path.join(temp_dir, "a0.mp4")
            cmd2 = [
                self.ffmpeg_path, "-y", "-i", a1_file, "-ss", "00:00:00", "-t", "0.333",
                "-vf", "scale=1080:2106:force_original_aspect_ratio=decrease,pad=1080:2106:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                "-movflags", "+faststart", "-flags", "+cgop", "-init_qpP", "0",
                "-quality", "best", "-rc", "cbr", "-threads", "0", a0_file
            ]
            self.run_cmd(cmd2)

            # 步骤3: 处理背景视频
            self.log(f"第三步处理中...")
            b1_file = os.path.join(temp_dir, "b1.mp4")
            cmd3 = [
                self.ffmpeg_path, "-y", "-stream_loop", "-1", "-i", background_file,
                "-vf", "scale=1080:2106:force_original_aspect_ratio=increase,crop=1080:2106:(iw-ow)/2:(ih-oh)/2,setsar=1:1,fps=60",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                "-deblock", "1:1", "-tune", "grain", "-an", "-threads", "0",
                "-t", "10.005000", b1_file
            ]
            self.run_cmd(cmd3)

            # 步骤4: 视频拼接
            self.log(f"第四步处理中...")
            a0b1_file = os.path.join(temp_dir, "a0b1.mp4")
            cmd4 = [
                self.ffmpeg_path, "-y", "-i", a0_file, "-i", b1_file,
                "-filter_complex", "[0:v][1:v]concat=n=2:v=1:a=0,trim=duration=10.005000",
                "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                "-deblock", "1:1", "-tune", "grain", "-threads", "0",
                "-t", "10.005000", a0b1_file
            ]
            self.run_cmd(cmd4)

            # 步骤5: 闪烁效果叠加
            self.log(f"第五步处理中...")
            a1b1_file = os.path.join(temp_dir, "a1b1.mp4")
            cmd5 = [
                self.ffmpeg_path, "-y", "-i", a1_file, "-i", a0b1_file,
                "-filter_complex", "[0:v]setpts=PTS-STARTPTS,fps=60[base];[1:v]setpts=PTS-STARTPTS,format=yuv420p,format=rgba,colorchannelmixer=aa=0.5[flash];[base][flash]overlay=enable='if(eq(mod(n,2),1),1,0)'[outv]",
                "-map", "[outv]", "-map", "0:a", "-c:v", "libx264", "-preset", "faster",
                "-crf", "23", "-x264-params", "level=6.2:ref=4", "-c:a", "copy",
                "-threads", "0", "-t", "10.005000", "-video_track_timescale", "1000k", a1b1_file
            ]
            self.run_cmd(cmd5)

            # 步骤6: 时间轴调整
            self.log(f"第六步处理中...")
            a1b1c1_file = os.path.join(temp_dir, "a1b1c1.mp4")
            cmd6 = [
                self.ffmpeg_path, "-y", "-i", a1b1_file,
                "-vf", "[0:v]setpts='N/FRAME_RATE/TB-666.5',select='not(eq(n,0))'",
                "-vsync", "0", "-global_quality", "23", "-c:v", "libx264",
                "-preset", "faster", "-movflags", "faststart", "-t", "10.005000",
                "-video_track_timescale", "1000k", a1b1c1_file
            ]
            self.run_cmd(cmd6)

            # 步骤7: 最终合成
            self.log(f"第七步处理中...")
            output_file = os.path.join(output_dir, f"{timestamp}-{basename}_屌大AB_{os.path.basename(main_file)}")
            cmd7 = [
                self.ffmpeg_path, "-y", "-i", a0b1_file, "-i", a1b1c1_file,
                "-map", "0:v:0", "-map", "1:v:0", "-map", "1:a:0", "-c", "copy",
                "-movflags", "faststart", "-disposition:v:0", "0",
                "-disposition:v:1", "default", "-disposition:a:1", "default",
                "-t", "10.005000", output_file
            ]
            self.run_cmd(cmd7)
            
            self.log(f"处理完成：{output_file}")

    def run_processing_logic(self, main_list, aux_list, output_dir, is_batch=False, del_a=False, del_b=False):
        """主处理逻辑"""
        try:
            self.log("屌大AB处理中请等待")
            
            if is_batch:
                video_extensions = ('.mp4', '.mkv', '.mov', '.avi')
                main_files = sorted([f for f in os.listdir(main_list[0]) if f.lower().endswith(video_extensions)])
                
                if not main_files:
                    self.log("主视频文件夹中未找到视频文件！")
                    return
                
                # 检查背景视频文件夹
                if not os.path.isdir(aux_list[0]):
                    self.log("背景视频路径不是有效的文件夹！")
                    return
                
                # 获取背景视频文件列表
                background_files = sorted([f for f in os.listdir(aux_list[0]) if f.lower().endswith(video_extensions)])
                if not background_files:
                    self.log("背景视频文件夹中未找到视频文件！")
                    return
                
                self.log(f"找到 {len(main_files)} 个主视频文件，{len(background_files)} 个背景视频文件，开始批量处理...")
                
                # 确保主视频和背景视频数量一致
                min_count = min(len(main_files), len(background_files))
                if min_count == 0:
                    self.log("没有可匹配的视频文件对！")
                    return
                
                self.log(f"开始处理 {min_count} 对视频文件...")
                
                for idx in range(min_count):
                    try:
                        main_file = os.path.join(main_list[0], main_files[idx])
                        background_file = os.path.join(aux_list[0], background_files[idx])
                        basename = os.path.splitext(main_files[idx])[0]
                        
                        self.log(f"[{idx+1}/{min_count}] 处理文件：{main_files[idx]}，背景：{background_files[idx]}")
                        self.process_single_video(main_file, background_file, output_dir)
                        self.log(f"✔️ 完成：{basename}")
                    except Exception as e:
                        self.log(f"❌ 失败：{main_files[idx]}，原因：{e}")
            else:
                basename = os.path.splitext(os.path.basename(main_list[0]))[0]
                self.process_single_video(main_list[0], aux_list[0], output_dir)
            
            self.log("屌大AB处理完成")
        except Exception as e:
            self.log(f"出错：{e}") 