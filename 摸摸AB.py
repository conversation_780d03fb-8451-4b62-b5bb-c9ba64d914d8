import os
import sys
import threading
import queue
import shutil
import tempfile
import subprocess
from datetime import datetime

def get_ffmpeg_path():
    # 尝试从主程序获取ffmpeg路径
    try:
        import 屌炸天V3_3
        if hasattr(屌炸天V3_3, 'ffmpeg_path'):
            return 屌炸天V3_3.ffmpeg_path
    except:
        pass
    
    exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    ffmpeg_path = os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffmpeg.exe')
    if os.path.exists(ffmpeg_path):
        return ffmpeg_path
    # 尝试其他可能的路径
    ffmpeg_path = os.path.join(exe_dir, 'ffmpeg', 'ffmpeg.exe')
    if os.path.exists(ffmpeg_path):
        return ffmpeg_path
    raise FileNotFoundError(f"未找到ffmpeg: {ffmpeg_path}")

def get_ffprobe_path():
    # 尝试从主程序获取ffprobe路径
    try:
        import 屌炸天V3_3
        if hasattr(屌炸天V3_3, 'ffprobe_path'):
            return 屌炸天V3_3.ffprobe_path
    except:
        pass
    
    exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    ffprobe_path = os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffprobe.exe')
    if os.path.exists(ffprobe_path):
        return ffprobe_path
    # 尝试其他可能的路径
    ffprobe_path = os.path.join(exe_dir, 'ffmpeg', 'ffprobe.exe')
    if os.path.exists(ffprobe_path):
        return ffprobe_path
    raise FileNotFoundError(f"未找到ffprobe: {ffprobe_path}")

class 摸摸AB:
    def __init__(self):
        self.main_file = ""
        self.aux_file = ""
        self.main_dir = ""
        self.aux_dir = ""
        self.output_dir = ""
        self.log_queue = queue.Queue()
        self.batch_mode_var = False

    def log(self, msg):
        print(msg)  # 简单的日志输出

    def get_video_info(self, video_path):
        ffprobe_path = getattr(self, 'ffprobe_path', None) or get_ffprobe_path()
        # 获取时长
        cmd_dur = [ffprobe_path, '-v', 'quiet', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path]
        duration = float(subprocess.run(cmd_dur, capture_output=True, text=True).stdout.strip())
        return duration

    def _run_ffmpeg_pipeline(self, main_file, aux_file, basename):
        """执行完整的FFmpeg处理流程"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        
        # 获取主视频时长和尺寸
        duration = self.get_video_info(main_file)
        duration_str = f"{duration:.6f}"
        
        # 获取主视频分辨率
        ffprobe_path = getattr(self, 'ffprobe_path', None) or get_ffprobe_path()
        cmd_res = [ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'csv=p=0', main_file]
        res = subprocess.run(cmd_res, capture_output=True, text=True).stdout.strip()
        width, height = map(int, res.split(','))
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 1. 处理主视频
            self.log(f"[1/7] 处理主视频...")
            temp_a = os.path.join(temp_dir, "temp_a.MP4")
            ffmpeg_path = getattr(self, 'ffmpeg_path', None) or get_ffmpeg_path()
            cmd1 = [
                ffmpeg_path, "-y", "-i", main_file,
                "-vf", f"scale={width}:{height},setsar=1:1,fps=30",
                "-map_metadata", "-1", "-c:v", "libx264", "-b:v", "6000k",
                "-maxrate", "6000k", "-minrate", "6000k", "-bufsize", "8000k",
                "-x264-params", "stitchable=1", temp_a
            ]
            self.run_cmd(cmd1)

            # 2. 处理副视频
            self.log(f"[2/7] 处理副视频...")
            temp_b = os.path.join(temp_dir, "temp_b.MP4")
            cmd2 = [
                ffmpeg_path, "-y", "-stream_loop", "1", "-i", aux_file,
                "-t", duration_str,
                "-vf", f"scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1:1,fps=30",
                "-map_metadata", "-1", "-c:v", "libx264", "-b:v", "4000k",
                "-maxrate", "4000k", "-minrate", "4000k", "-bufsize", "8000k",
                "-x264-params", "stitchable=1", "-c:a", "aac", "-b:a", "192k", temp_b
            ]
            self.run_cmd(cmd2)

            # 3. 视频交错合并
            self.log(f"[3/7] 视频交错合并...")
            temp_c = os.path.join(temp_dir, "temp_c.MP4")
            cmd3 = [
                ffmpeg_path, "-y", "-fflags", "+genpts", "-i", temp_a, "-i", temp_b,
                "-avoid_negative_ts", "make_zero",
                "-filter_complex", "[0:v]format=yuv420p[v0];[1:v]format=yuv420p[v1];[v0][v1]interleave,setsar=1:1",
                "-vsync", "vfr", "-map", "0:a", "-c:a", "copy", "-map_metadata", "-1",
                "-c:v", "libx264", "-b:v", "6000k", "-maxrate", "6000k", "-minrate", "6000k",
                "-bufsize", "8000k", "-x264-params", "stitchable=1", "-f", "mov", temp_c
            ]
            self.run_cmd(cmd3)

            # 4. 音频混合
            self.log(f"[4/7] 音频混合...")
            temp_audio = os.path.join(temp_dir, "temp_audio.aac")
            cmd4 = [
                ffmpeg_path, "-y", "-i", temp_c, "-i", temp_b,
                "-filter_complex", "[0:a]volume=0.98[main];[1:a]volume=0.03,apad=whole_dur=" + duration_str + "[bg];[main][bg]amix=inputs=2:duration=first:dropout_transition=0[outa]",
                "-map", "[outa]", "-c:a", "aac", "-b:a", "192k", temp_audio
            ]
            self.run_cmd(cmd4)

            # 5. 视频叠加效果
            self.log(f"[5/7] 视频叠加效果...")
            temp5 = os.path.join(temp_dir, "temp5.mp4")
            cmd5 = [
                ffmpeg_path, "-y", "-i", temp_b, "-i", temp_c,
                "-filter_complex", "[0:v]setparams=color_primaries=bt709:color_trc=bt709:colorspace=bt709,format=yuva420p,fps=60,colorchannelmixer=aa=0.05[bg1];[1:v]setparams=color_primaries=bt709:color_trc=bt709:colorspace=bt709,format=yuva420p[fg];[bg1][fg]overlay[out]",
                "-map", "[out]", "-c:v", "libx264", temp5
            ]
            self.run_cmd(cmd5)

            # 6. 最终合成
            self.log(f"[6/7] 最终合成...")
            out_file = os.path.join(self.output_dir, f"{timestamp}-{basename}_摸摸AB.mp4")
            cmd6 = [
                ffmpeg_path, "-y", "-i", temp_b, "-i", temp5, "-i", temp_audio,
                "-map", "0:v:0", "-map", "0:v:0", "-map", "1:v:0", "-map", "2:a?",
                "-c", "copy", "-movflags", "faststart",
                "-disposition:v:0", "metadata", "-disposition:v:1", "0",
                "-disposition:v:2", "default+forced", "-disposition:a:0", "default",
                out_file
            ]
            self.run_cmd(cmd6)
            
            self.log(f"[7/7] 处理完成！输出文件：{out_file}")

    def run_cmd(self, cmd):
        """执行FFmpeg命令"""
        # 创建STARTUPINFO对象来隐藏控制台窗口
        startupinfo = None
        if hasattr(subprocess, 'STARTUPINFO'):
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
        
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore',
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW if hasattr(subprocess, 'CREATE_NO_WINDOW') else 0
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{' '.join(cmd)}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")

    def process_video(self, is_batch):
        try:
            if is_batch:
                video_extensions = ('.mp4', '.mkv', '.mov', '.avi')
                main_list = sorted([f for f in os.listdir(self.main_dir) if f.lower().endswith(video_extensions)])
                aux_list = sorted([f for f in os.listdir(self.aux_dir) if f.lower().endswith(video_extensions)])
                
                if not main_list or not aux_list:
                    self.log("主/副视频文件夹中未找到视频文件！")
                    return
                
                min_len = min(len(main_list), len(aux_list))
                for idx in range(min_len):
                    try:
                        main_file = os.path.join(self.main_dir, main_list[idx])
                        aux_file = os.path.join(self.aux_dir, aux_list[idx])
                        basename = os.path.splitext(main_list[idx])[0]
                        self.log(f"[{idx+1}/{min_len}] 主：{main_list[idx]} 副：{aux_list[idx]}")
                        self._run_ffmpeg_pipeline(main_file, aux_file, basename)
                        self.log(f"✔️ 完成：{basename}")
                    except Exception as e:
                        self.log(f"❌ 失败：{main_list[idx]} + {aux_list[idx]}，原因：{e}")
                
                self.log("摸摸AB批量处理完成！")
            else:
                basename = os.path.splitext(os.path.basename(self.main_file))[0]
                self._run_ffmpeg_pipeline(self.main_file, self.aux_file, basename)
                self.log("摸摸AB处理完成！")
        except Exception as e:
            self.log(f"出错：{e}")

if __name__ == "__main__":
    # 当作为独立程序运行时，创建GUI界面
    class 摸摸AB_GUI(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title("摸摸AB - 复杂视频叠加特效合成工具")
            self.geometry("800x600")
            self.app = 摸摸AB()
            self.setup_ui()
            self.after(100, self.process_log_queue)
            
        def setup_ui(self):
            # 这里可以添加GUI界面代码
            pass
            
        def process_log_queue(self):
            # 处理日志队列
            pass
            
    app = 摸摸AB_GUI()
    app.mainloop() 