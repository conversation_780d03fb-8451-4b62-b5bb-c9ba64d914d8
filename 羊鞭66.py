from PyQt5.QtWidgets import (QApp<PERSON>, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QLineEdit,
                            QFileDialog, QMessageBox, QProgressBar, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon
import sys
import subprocess
import os
import time

class VideoProcessorThread(QThread):
    progress_updated = pyqtSignal(str)
    processing_finished = pyqtSignal(bool, str)

    def __init__(self, ffmpeg_cmd):
        super().__init__()
        self.ffmpeg_cmd = ffmpeg_cmd

    def run(self):
        try:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE

            process = subprocess.Popen(
                self.ffmpeg_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8',
                errors='ignore',
                startupinfo=startupinfo,
                creationflags=subprocess.CREATE_NO_WINDOW
            )

            error_output = []
            for line in process.stdout:
                if "frame=" in line:
                    self.progress_updated.emit(line.strip())
                elif "error" in line.lower() or "failed" in line.lower():
                    error_output.append(line.strip())

            process.wait()

            if process.returncode == 0:
                self.processing_finished.emit(True, "处理完成")
            else:
                error_msg = "处理失败"
                if error_output:
                    error_msg += f": {'; '.join(error_output[-3:])}"  # 显示最后3个错误信息
                else:
                    error_msg += f" (返回码: {process.returncode})"
                self.processing_finished.emit(False, error_msg)

        except Exception as e:
            self.processing_finished.emit(False, f"处理错误: {str(e)}")



class VideoProcessorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.processing_thread = None
        self.current_process = None
        self.ffmpeg_cmd1 = None
        self.ffmpeg_cmd2 = None
        self.ffmpeg_cmd3 = None
        self.ffmpeg_cmd4 = None
        self.current_stage = 0
        self.total_stages = 8  # 羊鞭AB八阶段处理

        # 多线程处理标志
        self.use_multithreading = True
        self.max_workers = 3  # 最大并发数

        # 设置窗口标题和大小
        self.setWindowTitle("羊鞭视频批量处理工具")
        self.setGeometry(100, 100, 800, 600)

        # 设置窗口图标
        if os.path.exists("icon.png"):
            self.setWindowIcon(QIcon("icon.png"))

        # 应用样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 14px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QLineEdit {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QStatusBar {
                background-color: #e0e0e0;
                color: #333;
            }
        """)

        # 创建主控件
        self.main_widget = QWidget()
        self.main_widget.setStyleSheet("background-color: white; border-radius: 8px; padding: 20px;")
        self.setCentralWidget(self.main_widget)

        # 主布局
        self.main_layout = QVBoxLayout()
        self.main_widget.setLayout(self.main_layout)

        # 标题
        title_label = QLabel("羊鞭AB视频剪辑工具，请勿非法使用")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #333;
            padding-bottom: 15px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        self.main_layout.addWidget(title_label)
        self.main_layout.addSpacing(20)

        # 主视频文件夹选择
        self.create_folder_selection("主视频文件夹:", "main_video_folder")

        # 辅助视频文件夹选择
        self.create_folder_selection("辅助视频文件夹:", "aux_video_folder")

        # 输出文件夹选择
        self.create_folder_selection("输出文件夹:", "output_folder", is_output=True)

        # 处理模式选择
        self.create_processing_mode_selection()

        # 多线程设置
        self.create_multithreading_settings()

        # 速度优化设置
        self.create_speed_optimization_settings()

        # 处理按钮
        process_btn = QPushButton("开始处理视频")
        process_btn.setStyleSheet("""
            font-size: 16px;
            background-color: #2196F3;
        """)
        process_btn.clicked.connect(self.process_videos)
        self.main_layout.addSpacing(20)
        self.main_layout.addWidget(process_btn, alignment=Qt.AlignCenter)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
            }
        """)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.main_layout.addSpacing(10)
        self.main_layout.addWidget(self.progress_bar)

        # 状态栏
        self.statusBar().showMessage("就绪")

    def create_processing_mode_selection(self):
        """创建处理模式选择控件组"""
        container = QWidget()
        container.setStyleSheet("background-color: #f9f9f9; border-radius: 6px; padding: 8px;")
        layout = QHBoxLayout(container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 标签
        label = QLabel("处理模式:")
        label.setFixedWidth(120)
        label.setStyleSheet("font-weight: bold; color: #555;")
        layout.addWidget(label)

        # 创建按钮组
        self.mode_button_group = QButtonGroup()

        # 标准模式单选按钮
        self.standard_mode_radio = QRadioButton("羊鞭天下")
        self.standard_mode_radio.setChecked(True)  # 默认选中
        self.standard_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.standard_mode_radio, 0)
        layout.addWidget(self.standard_mode_radio)

        # 魔法AB模式单选按钮
        self.magic_ab_mode_radio = QRadioButton("巨薯一号")
        self.magic_ab_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.magic_ab_mode_radio, 1)
        layout.addWidget(self.magic_ab_mode_radio)

        # 八卦模式单选按钮
        self.bagua_mode_radio = QRadioButton("八卦模式")
        self.bagua_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.bagua_mode_radio, 2)
        layout.addWidget(self.bagua_mode_radio)

        # 乘风AB模式单选按钮
        self.chengfeng_ab_mode_radio = QRadioButton("乘风AB")
        self.chengfeng_ab_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.chengfeng_ab_mode_radio, 3)
        layout.addWidget(self.chengfeng_ab_mode_radio)

        # 牛牛AB模式单选按钮
        self.kaitian_ab_mode_radio = QRadioButton("牛牛AB")
        self.kaitian_ab_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.kaitian_ab_mode_radio, 4)
        layout.addWidget(self.kaitian_ab_mode_radio)

        # 安琪拉模式单选按钮
        self.angela_mode_radio = QRadioButton("安琪拉")
        self.angela_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.angela_mode_radio, 5)
        layout.addWidget(self.angela_mode_radio)

        # 双流模式单选按钮
        self.dual_stream_mode_radio = QRadioButton("双流模式")
        self.dual_stream_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.dual_stream_mode_radio, 6)
        layout.addWidget(self.dual_stream_mode_radio)

        # 季度213模式单选按钮
        self.jidu213_mode_radio = QRadioButton("季度213")
        self.jidu213_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #d63384;
                spacing: 5px;
                font-weight: bold;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.jidu213_mode_radio, 7)
        layout.addWidget(self.jidu213_mode_radio)

        # 季度SB模式单选按钮
        self.jidusb_mode_radio = QRadioButton("季度SB")
        self.jidusb_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #dc3545;
                spacing: 5px;
                font-weight: bold;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.jidusb_mode_radio, 8)
        layout.addWidget(self.jidusb_mode_radio)

        # 大羊鞭模式单选按钮
        self.dayangbian_mode_radio = QRadioButton("大羊鞭")
        self.dayangbian_mode_radio.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #8b0000;
                spacing: 5px;
                font-weight: bold;
                background-color: rgba(139, 0, 0, 0.1);
                border-radius: 4px;
                padding: 2px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.mode_button_group.addButton(self.dayangbian_mode_radio, 9)
        layout.addWidget(self.dayangbian_mode_radio)



        # 添加弹性空间
        layout.addStretch()

        # 添加间距
        self.main_layout.addSpacing(10)
        self.main_layout.addWidget(container)

    def create_multithreading_settings(self):
        """创建多线程设置控件组"""
        container = QWidget()
        container.setStyleSheet("background-color: #f9f9f9; border-radius: 6px; padding: 8px;")
        layout = QHBoxLayout(container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 标签
        label = QLabel("多线程处理:")
        label.setFixedWidth(120)
        label.setStyleSheet("font-weight: bold; color: #555;")
        layout.addWidget(label)

        # 多线程开关
        self.multithreading_checkbox = QRadioButton("FFmpeg多线程加速")
        self.multithreading_checkbox.setChecked(True)  # 默认启用
        self.multithreading_checkbox.setEnabled(True)  # 启用控件
        self.multithreading_checkbox.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #333;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.multithreading_checkbox.toggled.connect(self.on_multithreading_toggled)
        layout.addWidget(self.multithreading_checkbox)

        # 线程数标签 - 显示24线程
        self.threads_label = QLabel("编码线程: 24")
        self.threads_label.setStyleSheet("font-size: 14px; color: #666;")
        layout.addWidget(self.threads_label)

        # 添加弹性空间
        layout.addStretch()

        # 添加间距
        self.main_layout.addSpacing(10)
        self.main_layout.addWidget(container)

    def on_multithreading_toggled(self, checked):
        """多线程开关切换"""
        self.use_multithreading = checked
        if checked:
            self.threads_label.setText("编码线程: 24")
            self.threads_label.setStyleSheet("font-size: 14px; color: #666;")
        else:
            self.threads_label.setText("编码线程: 1")
            self.threads_label.setStyleSheet("font-size: 14px; color: #999;")

    def on_gpu_acceleration_toggled(self, checked):
        """GPU加速开关切换"""
        if checked:
            self.speed_label.setText("GPU + CPU协同加速")
            self.speed_label.setStyleSheet("font-size: 14px; color: #856404; font-weight: bold;")
        else:
            self.speed_label.setText("仅CPU加速")
            self.speed_label.setStyleSheet("font-size: 14px; color: #856404;")

    def create_speed_optimization_settings(self):
        """创建速度优化设置控件组"""
        container = QWidget()
        container.setStyleSheet("background-color: #fff3cd; border-radius: 6px; padding: 8px;")
        layout = QHBoxLayout(container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 标签
        label = QLabel("速度优化:")
        label.setFixedWidth(120)
        label.setStyleSheet("font-weight: bold; color: #856404;")
        layout.addWidget(label)

        # GPU加速开关
        self.gpu_acceleration_checkbox = QRadioButton("启用GPU硬件加速")
        self.gpu_acceleration_checkbox.setChecked(True)  # 默认启用
        self.gpu_acceleration_checkbox.setStyleSheet("""
            QRadioButton {
                font-size: 14px;
                color: #856404;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
            }
        """)
        self.gpu_acceleration_checkbox.toggled.connect(self.on_gpu_acceleration_toggled)
        layout.addWidget(self.gpu_acceleration_checkbox)

        # 速度等级标签
        self.speed_label = QLabel("自动检测最快编码器")
        self.speed_label.setStyleSheet("font-size: 14px; color: #856404;")
        layout.addWidget(self.speed_label)

        # 添加弹性空间
        layout.addStretch()

        # 添加间距
        self.main_layout.addSpacing(10)
        self.main_layout.addWidget(container)

    def create_folder_selection(self, label_text, field_name, is_output=False):
        """创建文件夹选择控件组"""
        container = QWidget()
        container.setStyleSheet("background-color: #f9f9f9; border-radius: 6px; padding: 8px;")
        layout = QHBoxLayout(container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(10)

        # 标签
        label = QLabel(label_text)
        label.setFixedWidth(120)
        label.setStyleSheet("font-weight: bold; color: #555;")
        layout.addWidget(label)

        # 文件夹路径显示
        line_edit = QLineEdit()
        line_edit.setReadOnly(True)
        line_edit.setStyleSheet("""
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px;
        """)
        setattr(self, f"{field_name}_path", line_edit)
        layout.addWidget(line_edit, stretch=1)

        # 浏览按钮
        btn = QPushButton("浏览...")
        btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
            QPushButton:pressed {
                background-color: #455A64;
            }
        """)
        if is_output:
            btn.clicked.connect(lambda: self.select_output_folder(line_edit))
        else:
            btn.clicked.connect(lambda: self.select_input_folder(line_edit))
        layout.addWidget(btn)

        # 添加间距
        self.main_layout.addSpacing(10)

        self.main_layout.addWidget(container)

    def select_input_folder(self, line_edit):
        """选择输入文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder_path:
            line_edit.setText(folder_path)

    def select_output_folder(self, line_edit):
        """选择输出文件夹"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder_path:
            line_edit.setText(folder_path)

    def get_video_resolution(self, video_path):
        """使用ffprobe获取视频分辨率"""
        try:
            cmd = [
                ".\\ffmpeg\\ffprobe.exe",
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height",
                "-of", "csv=s=x:p=0",
                video_path
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')
            return result.stdout.strip()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取视频分辨率失败: {str(e)}")
            return None

    def get_video_duration(self, video_path):
        """使用ffprobe获取视频时长(秒)"""
        try:
            cmd = [
                ".\\ffmpeg\\ffprobe.exe",
                "-v", "error",
                "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1",
                video_path
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')

            # 调试信息
            print(f"ffprobe命令: {' '.join(cmd)}")
            print(f"返回码: {result.returncode}")
            print(f"标准输出: '{result.stdout}'")
            print(f"错误输出: '{result.stderr}'")

            # 检查命令是否成功执行
            if result.returncode != 0:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                raise Exception(f"ffprobe执行失败 (返回码: {result.returncode}): {error_msg}")

            # 检查输出是否为空
            output = result.stdout.strip()
            if not output:
                raise Exception("ffprobe返回空结果，可能视频文件损坏或格式不支持")

            # 尝试转换为浮点数
            try:
                # 清理输出：去除所有空白字符（包括换行符、空格等）
                clean_output = ''.join(output.split())
                print(f"清理后的输出: '{clean_output}'")

                duration = float(clean_output)
                if duration <= 0:
                    raise Exception(f"视频时长无效: {duration}秒")
                print(f"成功获取视频时长: {duration}秒")
                return duration
            except ValueError as ve:
                raise Exception(f"无法解析时长值: '{output}' -> '{clean_output}' (ValueError: {str(ve)})")

        except Exception as e:
            QMessageBox.critical(self, "错误",
                f"获取视频时长失败:\n{str(e)}\n\n"
                f"视频文件: {video_path}\n\n"
                "请检查:\n"
                "1. ffprobe.exe在当前目录的ffmpeg文件夹中\n"
                "2. 视频文件路径正确且文件完整\n"
                "3. 视频格式被ffprobe支持\n"
                "4. 有足够的文件访问权限")
            return None

    def get_video_dimensions(self, video_path):
        """使用ffprobe获取视频尺寸(宽度, 高度)"""
        try:
            cmd = [
                ".\\ffmpeg\\ffprobe.exe",
                "-v", "error",
                "-select_streams", "v:0",
                "-show_entries", "stream=width,height",
                "-of", "csv=s=x:p=0",
                video_path
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8', errors='ignore')

            if result.returncode != 0:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                raise Exception(f"ffprobe执行失败: {error_msg}")

            output = result.stdout.strip()
            if not output:
                raise Exception("ffprobe返回空输出")

            # 解析输出格式: "width x height"
            dimensions = output.split('x')
            if len(dimensions) != 2:
                raise Exception(f"无法解析尺寸输出: {output}")

            width = int(dimensions[0])
            height = int(dimensions[1])

            if width <= 0 or height <= 0:
                raise Exception(f"视频尺寸无效: {width}x{height}")

            return width, height

        except Exception as e:
            raise Exception(f"获取视频尺寸失败: {str(e)}")

    def get_video_files(self, folder_path):
        """获取文件夹中的视频文件列表"""
        if not folder_path:
            return []

        video_exts = ('.mp4', '.avi', '.mov')
        return sorted([
            os.path.join(folder_path, f)
            for f in os.listdir(folder_path)
            if f.lower().endswith(video_exts)
        ])

    def check_system_resources(self):
        """检查系统资源是否充足（简化版本，不依赖psutil）"""
        try:
            # 简单的磁盘空间检查
            import shutil
            total, used, _ = shutil.disk_usage('.')  # 使用 _ 忽略 free 变量
            disk_usage_percent = (used / total) * 100

            if disk_usage_percent > 90:
                self.statusBar().showMessage(f"磁盘空间不足 ({disk_usage_percent:.1f}%)，请清理磁盘空间")
                return False

            return True

        except Exception as e:
            print(f"资源检查失败: {str(e)}")
            return True



    def process_videos(self):
        """批量处理视频"""
        if self.processing_thread and self.processing_thread.isRunning():
            QMessageBox.warning(self, "警告", "已有处理任务在进行中!")
            return

        main_folder = self.main_video_folder_path.text()
        aux_folder = self.aux_video_folder_path.text()
        output_folder = self.output_folder_path.text()

        if not all([main_folder, aux_folder, output_folder]):
            QMessageBox.warning(self, "警告", "请先选择所有必要的文件夹!")
            return

        # 获取文件列表
        main_files = self.get_video_files(main_folder)
        aux_files = self.get_video_files(aux_folder)

        if not main_files or not aux_files:
            QMessageBox.warning(self, "警告", "文件夹中没有找到视频文件!")
            return

        # 创建输出文件夹
        import os
        os.makedirs(output_folder, exist_ok=True)

        # 处理文件对
        self.total_pairs = min(len(main_files), len(aux_files))
        self.processed_pairs = 0
        self.main_files = main_files
        self.aux_files = aux_files
        self.output_folder = output_folder

        # 检查处理模式
        self.is_magic_ab_mode = self.magic_ab_mode_radio.isChecked()
        self.is_bagua_mode = self.bagua_mode_radio.isChecked()
        self.is_chengfeng_ab_mode = self.chengfeng_ab_mode_radio.isChecked()
        self.is_kaitian_ab_mode = self.kaitian_ab_mode_radio.isChecked()
        self.is_angela_mode = self.angela_mode_radio.isChecked()
        self.is_dual_stream_mode = self.dual_stream_mode_radio.isChecked()
        self.is_jidu213_mode = self.jidu213_mode_radio.isChecked()
        self.is_jidusb_mode = self.jidusb_mode_radio.isChecked()
        self.is_dayangbian_mode = self.dayangbian_mode_radio.isChecked()

        # 为了避免软件卡死，暂时禁用多线程，使用原有的单线程处理
        # 多线程处理FFmpeg可能导致资源竞争和UI阻塞
        self.process_next_pair()

    def process_next_pair(self):
        if self.processed_pairs >= self.total_pairs:
            QMessageBox.information(self, "完成", f"批量处理已完成!\n共处理了 {self.total_pairs} 对视频")
            self.statusBar().showMessage("就绪")
            self.progress_bar.setValue(100)
            self.current_stage = 0
            # 强制垃圾回收
            import gc
            gc.collect()
            return

        # 检查系统资源
        if not self.check_system_resources():
            # 如果资源不足，延迟处理
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(5000, self.process_next_pair)  # 5秒后重试
            return

        # 重置进度
        self.progress_bar.setValue(0)
        self.current_stage = 0

        main_video = self.main_files[self.processed_pairs]
        aux_video = self.aux_files[self.processed_pairs]

        # 自动生成输出文件名
        import os
        from datetime import datetime
        main_name = os.path.splitext(os.path.basename(main_video))[0]
        aux_name = os.path.splitext(os.path.basename(aux_video))[0]

        # 为安琪拉模式生成特殊的文件名格式
        if self.is_angela_mode:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(
                self.output_folder,
                f"{timestamp}_安琪AB处理成功_{main_name}.mp4"
            )
        else:
            output_file = os.path.join(
                self.output_folder,
                f"{main_name}_+_{aux_name}.mp4"
            )

        self.statusBar().showMessage(f"准备处理 {self.processed_pairs+1}/{self.total_pairs}: {os.path.basename(main_video)}")

        try:
            # 根据模式选择不同的处理方式
            if self.is_magic_ab_mode:
                self.process_magic_ab_mode(main_video, aux_video, output_file)
            elif self.is_bagua_mode:
                self.process_bagua_mode(main_video, aux_video, output_file)
            elif self.is_chengfeng_ab_mode:
                self.process_chengfeng_ab_mode(main_video, aux_video, output_file)
            elif self.is_kaitian_ab_mode:
                self.process_kaitian_ab_mode(main_video, aux_video, output_file)
            elif self.is_angela_mode:
                self.process_angela_mode(main_video, aux_video, output_file)
            elif self.is_dual_stream_mode:
                self.process_dual_stream_mode(main_video, aux_video, output_file)
            elif self.is_jidu213_mode:
                self.process_jidu213_mode(main_video, aux_video, output_file)
            elif self.is_jidusb_mode:
                self.process_jidusb_mode(main_video, aux_video, output_file)
            elif self.is_dayangbian_mode:
                self.process_dayangbian_mode(main_video, aux_video, output_file)
            else:
                self.process_standard_mode(main_video, aux_video, output_file)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理过程中发生错误:\n{str(e)}")
            self.processed_pairs += 1
            self.process_next_pair()



    def process_magic_ab_mode(self, main_video, aux_video, output_file):
        """魔法AB模式处理"""
        # 获取主视频时长
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取主视频分辨率
        resolution = self.get_video_resolution(main_video)
        if not resolution:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 解析主视频分辨率
        try:
            width, height = resolution.split('x')
            main_width, main_height = int(width), int(height)
        except:
            QMessageBox.warning(self, "警告", f"无法解析主视频分辨率: {resolution}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()
        temp_b_path = os.path.join(temp_dir, "temp_B.MP4")
        temp_a_path = os.path.join(temp_dir, "temp_A.MP4")
        temp_c_path = os.path.join(temp_dir, "temp_C.MP4")

        # 检测GPU加速设置
        def detect_best_encoder():
            gpu_available = False
            gpu_encoder = "libx264"
            gpu_type = "CPU"

            # 检查是否启用GPU加速
            if self.gpu_acceleration_checkbox.isChecked():
                try:
                    # 检测GPU编码器
                    result = subprocess.run([".\\ffmpeg\\ffmpeg.exe", "-hide_banner", "-encoders"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        output = result.stdout
                        # 优先级：NVENC > QSV > AMF
                        if "h264_nvenc" in output:
                            gpu_encoder = "h264_nvenc"
                            gpu_type = "GPU-NVENC"
                            gpu_available = True
                        elif "h264_qsv" in output:
                            gpu_encoder = "h264_qsv"
                            gpu_type = "GPU-QSV"
                            gpu_available = True
                        elif "h264_amf" in output:
                            gpu_encoder = "h264_amf"
                            gpu_type = "GPU-AMF"
                            gpu_available = True
                except:
                    pass

            return gpu_encoder, gpu_type, gpu_available

        video_encoder, encoder_type, gpu_available = detect_best_encoder()

        # 显示加速类型
        if gpu_available and self.use_multithreading:
            accel_info = f"{encoder_type} + CPU多线程协同"
        elif gpu_available:
            accel_info = f"{encoder_type}加速"
        elif self.use_multithreading:
            accel_info = "CPU多线程加速"
        else:
            accel_info = "CPU单线程"

        self.statusBar().showMessage(f"多轨道AB处理 {self.processed_pairs+1}/{self.total_pairs} - 使用{accel_info}")

        # 检查输入文件是否存在
        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "错误", f"辅助视频文件不存在: {aux_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return
        # 复制输入文件到临时目录并重命名
        import shutil
        temp_right_path = os.path.join(temp_dir, "temp_right.mp4")
        temp_left_path = os.path.join(temp_dir, "temp_left.mp4")
        shutil.copy2(aux_video, temp_right_path)
        shutil.copy2(main_video, temp_left_path)

        # 第一阶段：处理辅助视频 (temp_B.MP4) - 辅助视频分辨率跟随主视频
        if gpu_available:
            # GPU加速版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_magic_stage1 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-stream_loop", "-1", "-i", temp_right_path,
                    "-t", str(duration),
                    "-vf", f"scale={main_width}:{main_height}:force_original_aspect_ratio=disable,setsar=1:1,fps=30",
                    "-r", "30",
                    "-map_metadata", "-1",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-b:v", "5000k",
                    "-maxrate", "5000k",
                    "-bufsize", "5000k",
                    "-c:a", "copy",
                    temp_b_path
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_magic_stage1 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-stream_loop", "-1", "-i", temp_right_path,
                    "-t", str(duration),
                    "-vf", f"scale={main_width}:{main_height}:force_original_aspect_ratio=disable,setsar=1:1,fps=30",
                    "-r", "30",
                    "-map_metadata", "-1",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-b:v", "5000k",
                    "-maxrate", "5000k",
                    "-bufsize", "5000k",
                    "-c:a", "copy",
                    temp_b_path
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_magic_stage1 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-stream_loop", "-1", "-i", temp_right_path,
                    "-t", str(duration),
                    "-vf", f"scale={main_width}:{main_height}:force_original_aspect_ratio=disable,setsar=1:1,fps=30",
                    "-r", "30",
                    "-map_metadata", "-1",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "5000k",
                    "-maxrate", "5000k",
                    "-bufsize", "5000k",
                    "-c:a", "copy",
                    temp_b_path
                ]
        else:
            # CPU版本（原始命令）
            self.ffmpeg_cmd_magic_stage1 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-stream_loop", "-1", "-i", temp_right_path,
                "-t", str(duration),
                "-vf", f"scale={main_width}:{main_height}:force_original_aspect_ratio=disable,setsar=1:1,fps=30",
                "-r", "30",
                "-map_metadata", "-1",
                "-c:v", "libx264",
                "-b:v", "5000k",
                "-maxrate", "5000k",
                "-bufsize", "5000k",
                "-x264opts", "nal-hrd=cbr",
                "-c:a", "copy",
                temp_b_path
            ]

        # 第二阶段：处理主视频 (temp_A.MP4) - 保持主视频原始分辨率
        if gpu_available:
            # GPU加速版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_magic_stage2 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", temp_left_path,
                    "-vf", f"setsar=1:1,scale={main_width}:{main_height},fps=30",
                    "-r", "30",
                    "-map_metadata", "-1",
                    "-f", "mp4",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-b:v", "5000k",
                    "-maxrate", "5000k",
                    "-bufsize", "10000k",
                    temp_a_path
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_magic_stage2 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", temp_left_path,
                    "-vf", f"setsar=1:1,scale={main_width}:{main_height},fps=30",
                    "-r", "30",
                    "-map_metadata", "-1",
                    "-f", "mp4",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-b:v", "5000k",
                    "-maxrate", "5000k",
                    "-bufsize", "10000k",
                    temp_a_path
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_magic_stage2 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", temp_left_path,
                    "-vf", f"setsar=1:1,scale={main_width}:{main_height},fps=30",
                    "-r", "30",
                    "-map_metadata", "-1",
                    "-f", "mp4",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "5000k",
                    "-maxrate", "5000k",
                    "-bufsize", "10000k",
                    temp_a_path
                ]
        else:
            # CPU版本（原始命令）
            self.ffmpeg_cmd_magic_stage2 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-i", temp_left_path,
                "-vf", f"setsar=1:1,scale={main_width}:{main_height},fps=30",
                "-r", "30",
                "-map_metadata", "-1",
                "-f", "mp4",
                "-c:v", "libx264",
                "-b:v", "5000k",
                "-maxrate", "5000k",
                "-bufsize", "10000k",
                "-x264opts", "nal-hrd=cbr",
                "-strict", "experimental",
                temp_a_path
            ]

        # 第三阶段：合并处理 (temp_C.MP4) - 使用新的双流映射命令
        self.ffmpeg_cmd_magic_stage3 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y",
            "-i", temp_a_path,
            "-i", temp_b_path,
            "-map", "0:v",
            "-map", "1:v",
            "-map", "0:a?",
            "-c", "copy",
            "-map_metadata", "-1",
            "-disposition:v:0", "forced",
            "-disposition:v:1", "default",
            temp_c_path
        ]

        # 第四阶段：最终多流输出 - 按照新的命令格式
        self.ffmpeg_cmd_magic_stage4 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y",
            "-i", temp_b_path,
            "-i", temp_b_path,
            "-i", temp_c_path,
            "-i", temp_b_path,
            "-map", "0:v:0",
            "-map", "1:v:0",
            "-map", "2:v:0",
            "-map", "3:v:0",
            "-map", "2:a:0",
            "-c", "copy",
            "-movflags", "faststart",
            "-disposition:v:0", "0",
            "-disposition:v:1", "0",
            "-disposition:v:2", "default",
            "-disposition:v:3", "0",
            "-disposition:a:0", "default",
            output_file
        ]

        # 调试信息：打印FFmpeg命令
        print(f"魔法AB第一阶段命令: {' '.join(self.ffmpeg_cmd_magic_stage1)}")

        # 执行魔法AB第一阶段处理
        self.execute_stage(self.ffmpeg_cmd_magic_stage1, "魔法AB第一阶段",
                         lambda success, msg: self.on_magic_ab_stage1_finished(success, msg, temp_a_path, temp_b_path, temp_c_path, output_file, temp_dir))

    def on_magic_ab_stage1_finished(self, success, message, temp_a_path, temp_b_path, temp_c_path, output_file, temp_dir):
        """魔法AB第一阶段完成回调"""
        import os
        if not success or not os.path.exists(temp_b_path):
            QMessageBox.warning(self, "警告", f"魔法AB第一阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第二阶段：处理主视频
        self.statusBar().showMessage(f"魔法AB第二阶段 {self.processed_pairs+1}/{self.total_pairs}: 处理主视频")

        # 执行第二阶段处理
        self.execute_stage(self.ffmpeg_cmd_magic_stage2, "魔法AB第二阶段",
                         lambda success, msg: self.on_magic_ab_stage2_finished(success, msg, temp_a_path, temp_b_path, temp_c_path, output_file, temp_dir))

    def on_magic_ab_stage2_finished(self, success, message, temp_a_path, temp_b_path, temp_c_path, output_file, temp_dir):
        """魔法AB第二阶段完成回调"""
        import os
        if not success or not os.path.exists(temp_a_path):
            QMessageBox.warning(self, "警告", f"魔法AB第二阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第三阶段：合并处理
        self.statusBar().showMessage(f"魔法AB第三阶段 {self.processed_pairs+1}/{self.total_pairs}: 合并处理")

        # 执行第三阶段处理
        self.execute_stage(self.ffmpeg_cmd_magic_stage3, "魔法AB第三阶段",
                         lambda success, msg: self.on_magic_ab_stage3_finished(success, msg, temp_c_path, output_file, temp_dir))

    def on_magic_ab_stage3_finished(self, success, message, temp_c_path, output_file, temp_dir):
        """魔法AB第三阶段完成回调"""
        import os
        if not success or not os.path.exists(temp_c_path):
            QMessageBox.warning(self, "警告", f"魔法AB第三阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第四阶段：最终多流输出
        self.statusBar().showMessage(f"魔法AB第四阶段 {self.processed_pairs+1}/{self.total_pairs}: 最终输出")

        # 执行第四阶段处理
        self.execute_stage(self.ffmpeg_cmd_magic_stage4, "魔法AB第四阶段",
                         lambda success, msg: self.on_magic_ab_stage4_finished(success, msg, output_file, temp_dir))

    def on_magic_ab_stage4_finished(self, success, message, output_file, temp_dir):
        """魔法AB第四阶段完成回调"""
        import shutil
        import os

        if success:
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.warning(self, "警告", f"魔法AB第四阶段失败: {message}")

        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"清理临时文件失败: {str(e)}")

        self.processed_pairs += 1
        self.process_next_pair()

    def cleanup_and_continue(self, temp_dir):
        """改进的清理临时文件并继续下一个"""
        import shutil
        import time
        import gc

        # 多次尝试清理临时文件
        for attempt in range(3):
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    print(f"成功清理临时文件: {temp_dir}")
                break
            except Exception as e:
                print(f"清理临时文件失败 (尝试 {attempt+1}/3): {str(e)}")
                if attempt < 2:
                    time.sleep(1)  # 等待1秒后重试

        # 强制垃圾回收释放内存
        gc.collect()

        # 添加短暂延迟，让系统释放资源
        time.sleep(0.5)

        self.processed_pairs += 1
        self.process_next_pair()

    def process_bagua_mode(self, main_video, aux_video, output_file):
        """八卦模式处理"""
        # 获取主视频时长
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        import subprocess
        temp_dir = tempfile.mkdtemp()

        # 检测并使用最佳编码器组合
        def detect_best_encoder():
            gpu_available = False
            gpu_encoder = "libx264"
            gpu_type = "CPU"

            # 检查是否启用GPU加速
            if self.gpu_acceleration_checkbox.isChecked():
                try:
                    # 检测GPU编码器
                    result = subprocess.run([".\\ffmpeg\\ffmpeg.exe", "-hide_banner", "-encoders"],
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        output = result.stdout
                        # 优先级：NVENC > QSV > AMF
                        if "h264_nvenc" in output:
                            gpu_encoder = "h264_nvenc"
                            gpu_type = "GPU-NVENC"
                            gpu_available = True
                        elif "h264_qsv" in output:
                            gpu_encoder = "h264_qsv"
                            gpu_type = "GPU-QSV"
                            gpu_available = True
                        elif "h264_amf" in output:
                            gpu_encoder = "h264_amf"
                            gpu_type = "GPU-AMF"
                            gpu_available = True
                except:
                    pass

            return gpu_encoder, gpu_type, gpu_available

        video_encoder, encoder_type, gpu_available = detect_best_encoder()

        # 显示加速类型
        if gpu_available and self.multithreading_checkbox.isChecked():
            accel_info = f"{encoder_type} + CPU多线程协同"
        elif gpu_available:
            accel_info = f"{encoder_type}加速"
        elif self.multithreading_checkbox.isChecked():
            accel_info = "CPU多线程加速"
        else:
            accel_info = "CPU单线程"

        # 创建临时文件路径
        temp_right_path = os.path.join(temp_dir, "temp_right.mp4")
        temp_left_path = os.path.join(temp_dir, "temp_left.mp4")
        left_1_path = os.path.join(temp_dir, "left_1.mp4")
        left_2_path = os.path.join(temp_dir, "left_2.mp4")
        right_1_path = os.path.join(temp_dir, "right_1.mp4")
        left_3_path = os.path.join(temp_dir, "left_3.mp4")

        self.statusBar().showMessage(f"八卦模式处理 {self.processed_pairs+1}/{self.total_pairs} - 使用{accel_info}")

        # 检查输入文件是否存在
        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "错误", f"辅助视频文件不存在: {aux_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 复制输入文件到临时目录并重命名
        import shutil
        shutil.copy2(aux_video, temp_right_path)
        shutil.copy2(main_video, temp_left_path)

        # 第一阶段：处理主视频并强制转换为4K (left_1.mp4)
        if gpu_available:
            # GPU编码器版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage1 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", temp_left_path,
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-pix_fmt", "yuv420p",
                    "-c:a", "copy",
                    left_1_path
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage1 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", temp_left_path,
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-pix_fmt", "yuv420p",
                    "-c:a", "copy",
                    left_1_path
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage1 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", temp_left_path,
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "8000k",
                    "-pix_fmt", "yuv420p",
                    "-c:a", "copy",
                    left_1_path
                ]
        else:
            # CPU编码器版本
            self.ffmpeg_cmd_bagua_stage1 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-i", temp_left_path,
                "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                "-c:v", "libx264",
                "-preset", "medium",
                "-x264-params", "crf=23",
                "-profile:v", "main",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                "-flags", "+cgop",
                "-tune", "ssim",
                "-c:a", "copy",
                left_1_path
            ]

        # 第二阶段：提取主视频片段 (left_2.mp4)
        if gpu_available:
            # GPU编码器版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage2 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_1_path,
                    "-ss", "00:00:00",
                    "-t", "0.333",
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    left_2_path
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage2 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_1_path,
                    "-ss", "00:00:00",
                    "-t", "0.333",
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    left_2_path
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage2 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_1_path,
                    "-ss", "00:00:00",
                    "-t", "0.333",
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "8000k",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    left_2_path
                ]
        else:
            # CPU编码器版本
            self.ffmpeg_cmd_bagua_stage2 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-i", left_1_path,
                "-ss", "00:00:00",
                "-t", "0.333",
                "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                "-c:v", "libx264",
                "-preset", "medium",
                "-x264-params", "crf=23",
                "-profile:v", "main",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                "-flags", "+cgop",
                "-tune", "ssim",
                "-an",
                left_2_path
            ]

        # 第三阶段：循环处理辅助视频并强制转换为4K (right_1.mp4)
        if gpu_available:
            # GPU编码器版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage3 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-stream_loop", "-1",
                    "-i", temp_right_path,
                    "-map_metadata", "-1",
                    "-t", str(duration),
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    right_1_path
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage3 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-stream_loop", "-1",
                    "-i", temp_right_path,
                    "-map_metadata", "-1",
                    "-t", str(duration),
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    right_1_path
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage3 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-stream_loop", "-1",
                    "-i", temp_right_path,
                    "-map_metadata", "-1",
                    "-t", str(duration),
                    "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "8000k",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    right_1_path
                ]
        else:
            # CPU编码器版本
            self.ffmpeg_cmd_bagua_stage3 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-stream_loop", "-1",
                "-i", temp_right_path,
                "-map_metadata", "-1",
                "-t", str(duration),
                "-vf", "scale=2160:3840:force_original_aspect_ratio=decrease,pad=2160:3840:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                "-c:v", "libx264",
                "-preset", "medium",
                "-x264-params", "crf=23",
                "-profile:v", "main",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                "-flags", "+cgop",
                "-tune", "ssim",
                "-an",
                right_1_path
            ]

        # 第四阶段：合并处理 (left_3.mp4)
        if gpu_available:
            # GPU编码器版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage4 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_2_path,
                    "-i", right_1_path,
                    "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    left_3_path
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage4 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_2_path,
                    "-i", right_1_path,
                    "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    left_3_path
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage4 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_2_path,
                    "-i", right_1_path,
                    "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "8000k",
                    "-pix_fmt", "yuv420p",
                    "-an",
                    left_3_path
                ]
        else:
            # CPU编码器版本
            self.ffmpeg_cmd_bagua_stage4 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-i", left_2_path,
                "-i", right_1_path,
                "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
                "-c:v", "libx264",
                "-preset", "medium",
                "-x264-params", "crf=23",
                "-profile:v", "main",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                "-flags", "+cgop",
                "-tune", "ssim",
                "-an",
                left_3_path
            ]

        # 第五阶段：最终交错处理
        if gpu_available:
            # GPU编码器版本
            if "nvenc" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage5 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_1_path,
                    "-i", left_3_path,
                    "-filter_complex", "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
                    "-map", "[v]",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-cq", "23",
                    "-pix_fmt", "yuv420p",
                    "-vsync", "vfr",
                    "-video_track_timescale", "1000000",
                    "-map", "0:a?",
                    "-c:a", "copy",
                    "-t", str(duration),
                    output_file
                ]
            elif "qsv" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage5 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_1_path,
                    "-i", left_3_path,
                    "-filter_complex", "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
                    "-map", "[v]",
                    "-c:v", video_encoder,
                    "-preset", "medium",
                    "-global_quality", "23",
                    "-pix_fmt", "yuv420p",
                    "-vsync", "vfr",
                    "-video_track_timescale", "1000000",
                    "-map", "0:a?",
                    "-c:a", "copy",
                    "-t", str(duration),
                    output_file
                ]
            elif "amf" in encoder_type.lower():
                self.ffmpeg_cmd_bagua_stage5 = [
                    ".\\ffmpeg\\ffmpeg.exe", "-y",
                    "-i", left_1_path,
                    "-i", left_3_path,
                    "-filter_complex", "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
                    "-map", "[v]",
                    "-c:v", video_encoder,
                    "-quality", "balanced",
                    "-rc", "cbr",
                    "-b:v", "8000k",
                    "-pix_fmt", "yuv420p",
                    "-vsync", "vfr",
                    "-video_track_timescale", "1000000",
                    "-map", "0:a?",
                    "-c:a", "copy",
                    "-t", str(duration),
                    output_file
                ]
        else:
            # CPU编码器版本
            self.ffmpeg_cmd_bagua_stage5 = [
                ".\\ffmpeg\\ffmpeg.exe", "-y",
                "-i", left_1_path,
                "-i", left_3_path,
                "-filter_complex", "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
                "-map", "[v]",
                "-c:v", "libx264",
                "-preset", "medium",
                "-x264-params", "crf=23",
                "-profile:v", "main",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                "-flags", "+cgop",
                "-tune", "ssim",
                "-vsync", "vfr",
                "-video_track_timescale", "1000000",
                "-map", "0:a?",
                "-c:a", "copy",
                "-t", str(duration),
                output_file
            ]

        # 调试信息：打印FFmpeg命令
        print(f"八卦模式第一阶段命令: {' '.join(self.ffmpeg_cmd_bagua_stage1)}")

        # 执行五阶段处理
        self.execute_stage(self.ffmpeg_cmd_bagua_stage1, "八卦第一阶段：处理主视频",
                         lambda success, msg: self.on_bagua_stage1_finished(success, msg, temp_dir, output_file))

    def on_bagua_stage1_finished(self, success, message, temp_dir, output_file):
        """八卦第一阶段完成回调"""
        import os
        if not success:
            QMessageBox.critical(self, "错误", f"八卦第一阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第二阶段
        self.execute_stage(self.ffmpeg_cmd_bagua_stage2, "八卦第二阶段：提取片段",
                         lambda success, msg: self.on_bagua_stage2_finished(success, msg, temp_dir, output_file))

    def on_bagua_stage2_finished(self, success, message, temp_dir, output_file):
        """八卦第二阶段完成回调"""
        if not success:
            QMessageBox.critical(self, "错误", f"八卦第二阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第三阶段
        self.execute_stage(self.ffmpeg_cmd_bagua_stage3, "八卦第三阶段：循环处理",
                         lambda success, msg: self.on_bagua_stage3_finished(success, msg, temp_dir, output_file))

    def on_bagua_stage3_finished(self, success, message, temp_dir, output_file):
        """八卦第三阶段完成回调"""
        if not success:
            QMessageBox.critical(self, "错误", f"八卦第三阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第四阶段
        self.execute_stage(self.ffmpeg_cmd_bagua_stage4, "八卦第四阶段：合并处理",
                         lambda success, msg: self.on_bagua_stage4_finished(success, msg, temp_dir, output_file))

    def on_bagua_stage4_finished(self, success, message, temp_dir, output_file):
        """八卦第四阶段完成回调"""
        if not success:
            QMessageBox.critical(self, "错误", f"八卦第四阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第五阶段
        self.execute_stage(self.ffmpeg_cmd_bagua_stage5, "八卦第五阶段：最终交错",
                         lambda success, msg: self.on_bagua_stage5_finished(success, msg, temp_dir, output_file))

    def on_bagua_stage5_finished(self, success, message, temp_dir, output_file):
        """八卦第五阶段完成回调"""
        import shutil
        import os

        if success:
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.critical(self, "错误", f"八卦第五阶段失败: {message}")

        # 清理临时文件并继续下一个
        self.cleanup_and_continue(temp_dir)

    def process_standard_mode(self, main_video, aux_video, output_file):
        """羊鞭天下模式处理 - 使用无尽AB的6步FFmpeg命令序列"""
        # 获取主视频信息
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取主视频分辨率（无尽AB使用主视频的分辨率）
        try:
            main_width, main_height = self.get_video_dimensions(main_video)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"无法获取主视频分辨率: {str(e)}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()

        # 创建无尽AB处理的临时文件路径
        left_1 = os.path.join(temp_dir, "left_1.mp4")
        left_2 = os.path.join(temp_dir, "left_2.mp4")
        right_1 = os.path.join(temp_dir, "right_1.mp4")
        left_3 = os.path.join(temp_dir, "left_3.mp4")

        self.statusBar().showMessage(f"无尽AB模式处理 {self.processed_pairs+1}/{self.total_pairs} - 使用CPU加速")

        # 检查输入文件是否存在
        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "错误", f"辅助视频文件不存在: {aux_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        if not os.path.exists(main_video):
            QMessageBox.warning(self, "错误", f"主视频文件不存在: {main_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 无尽AB第1步：视频去重处理（处理主视频）
        duration_str = f"{duration:.6f}"

        # 完全按照原无尽AB命令（仅CPU版本）
        self.ffmpeg_cmd1 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", main_video,
            "-vf", "fps=60",
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-c:a", "copy", left_1
        ]
        # 执行无尽AB第1步
        self.execute_stage(self.ffmpeg_cmd1, "无尽AB第1步：视频去重处理",
                         lambda success, msg: self.on_standard_stage1_finished(success, msg, main_video, aux_video, output_file, temp_dir, main_width, main_height, duration, duration_str, left_1, left_2, right_1, left_3))


    def on_standard_stage1_finished(self, success, message, main_video, aux_video, output_file, temp_dir, main_width, main_height, duration, duration_str, left_1, left_2, right_1, left_3):
        """无尽AB第1步完成回调"""
        import os

        if not success or not os.path.exists(left_1):
            QMessageBox.warning(self, "警告", f"无尽AB第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 无尽AB第2步：视频二次处理（提取0.333秒片段）
        vf3 = f'scale={main_width}:{main_height}:force_original_aspect_ratio=decrease,pad={main_width}:{main_height}:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60'

        # 完全按照原无尽AB命令（仅CPU版本）
        self.ffmpeg_cmd2 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", left_1,
            "-ss", "00:00:00", "-t", "0.333",
            "-vf", vf3,
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-an", left_2
        ]

        self.statusBar().showMessage(f"无尽AB第2步 {self.processed_pairs+1}/{self.total_pairs}: 视频二次处理")
        self.execute_stage(self.ffmpeg_cmd2, "无尽AB第2步：视频二次处理",
                         lambda success, msg: self.on_standard_stage2_finished(success, msg, aux_video, output_file, temp_dir, main_width, main_height, duration, duration_str, left_1, left_2, right_1, left_3))

    def on_standard_stage2_finished(self, success, message, aux_video, output_file, temp_dir, main_width, main_height, duration, duration_str, left_1, left_2, right_1, left_3):
        """无尽AB第2步完成回调"""
        import os

        if not success or not os.path.exists(left_2):
            QMessageBox.warning(self, "警告", f"无尽AB第2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 无尽AB第3步：循环去重处理（处理副视频）
        vf4 = f'scale={main_width}:{main_height}:force_original_aspect_ratio=disable,setsar=1:1,fps=60'

        # 完全按照原无尽AB命令（仅CPU版本）
        self.ffmpeg_cmd3 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-stream_loop", "-1", "-i", aux_video,
            "-map_metadata", "-1", "-t", duration_str,
            "-vf", vf4,
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-an", right_1
        ]

        self.statusBar().showMessage(f"无尽AB第3步 {self.processed_pairs+1}/{self.total_pairs}: 循环去重处理")
        self.execute_stage(self.ffmpeg_cmd3, "无尽AB第3步：循环去重处理",
                         lambda success, msg: self.on_standard_stage3_finished(success, msg, output_file, temp_dir, main_width, main_height, duration, duration_str, left_1, left_2, right_1, left_3))

    def on_standard_stage3_finished(self, success, message, output_file, temp_dir, main_width, main_height, duration, duration_str, left_1, left_2, right_1, left_3):
        """无尽AB第3步完成回调"""
        import os

        if not success or not os.path.exists(right_1):
            QMessageBox.warning(self, "警告", f"无尽AB第3步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 无尽AB第4步：独家算法融合
        # 检查输入文件是否存在且有效
        if not os.path.exists(left_2) or not os.path.exists(right_1):
            QMessageBox.warning(self, "错误", f"第4步输入文件不存在: left_2={os.path.exists(left_2)}, right_1={os.path.exists(right_1)}")
            self.cleanup_and_continue(temp_dir)
            return

        # 完全按照原无尽AB命令（仅CPU版本）
        self.ffmpeg_cmd4 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", left_2, "-i", right_1,
            "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration_str}",
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-an", left_3
        ]

        self.statusBar().showMessage(f"无尽AB第4步 {self.processed_pairs+1}/{self.total_pairs}: 独家算法融合")
        self.execute_stage(self.ffmpeg_cmd4, "无尽AB第4步：独家算法融合",
                         lambda success, msg: self.on_standard_stage4_finished(success, msg, output_file, temp_dir, duration, duration_str, left_1, left_2, right_1, left_3))

    def on_standard_stage4_finished(self, success, message, output_file, temp_dir, duration, duration_str, left_1, left_2, right_1, left_3):
        """无尽AB第4步完成回调"""
        import os

        if not success or not os.path.exists(left_3):
            QMessageBox.warning(self, "警告", f"无尽AB第4步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 无尽AB第5步：最终视频生成
        # 完全按照原无尽AB命令（仅CPU版本）
        self.ffmpeg_cmd5 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", left_1, "-i", left_3,
            "-filter_complex", "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
            "-map", "[v]",
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-vsync", "vfr",
            "-video_track_timescale", "1000000",
            "-map", "0:a?", "-c:a", "copy", "-t", duration_str, output_file
        ]

        self.statusBar().showMessage(f"无尽AB第5步 {self.processed_pairs+1}/{self.total_pairs}: 最终视频生成")
        self.execute_stage(self.ffmpeg_cmd5, "无尽AB第5步：最终视频生成",
                         lambda success, msg: self.on_standard_stage5_finished(success, msg, output_file, temp_dir))

    def on_standard_stage5_finished(self, success, message, output_file, temp_dir):
        """无尽AB第5步完成回调"""
        import shutil

        if success:
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.warning(self, "警告", f"无尽AB最终步骤失败: {message}")

        # 清理临时文件并继续下一个
        self.cleanup_and_continue(temp_dir)

    def generate_timestamps_file(self, total_frames, output_path):
        """生成羊鞭天下特殊时间戳文件"""
        timestamps = [0] * total_frames
        diff_pattern_odd = [32, 34, 34]
        diff_pattern_even = [34, 34, 32]

        if total_frames > 0: timestamps[0] = 1
        if total_frames > 1: timestamps[1] = 32

        for i in range(2, total_frames):
            group_index = (i // 2) - 1
            pattern_index = group_index % 3
            if i % 2 == 0: timestamps[i] = timestamps[i-2] + diff_pattern_odd[pattern_index]
            else: timestamps[i] = timestamps[i-2] + diff_pattern_even[pattern_index]

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write("# timecode format v4\n")
                for tc in timestamps: f.write(f"{tc}\n")
            return True
        except IOError as e:
            return False

    def get_frame_count(self, video_path):
        """获取视频帧数"""
        try:
            cmd = [
                ".\\ffmpeg\\ffprobe.exe",
                "-v", "error",
                "-select_streams", "v:0",
                "-count_packets",
                "-show_entries", "stream=nb_read_packets",
                "-of", "default=nokey=1:noprint_wrappers=1",
                video_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, encoding='utf-8', errors='ignore')
            frame_count = int(result.stdout.strip())
            return frame_count
        except Exception as e:
            return None

    def on_pojun_stage_finished(self, success, message, stage, output_file, temp_dir):
        """羊鞭AB阶段完成回调"""
        if not success:
            QMessageBox.warning(self, "警告", f"羊鞭AB第{stage}步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 根据当前阶段执行下一阶段
        if stage == 1:
            self.statusBar().showMessage(f"羊鞭AB第二步 {self.processed_pairs+1}/{self.total_pairs}: 处理B视频")
            self.execute_stage(self.ffmpeg_cmd2, "羊鞭AB第二步：处理B视频",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 2, output_file, temp_dir))
        elif stage == 2:
            self.statusBar().showMessage(f"羊鞭AB第三步 {self.processed_pairs+1}/{self.total_pairs}: 提取尾部")
            self.execute_stage(self.ffmpeg_cmd3, "羊鞭AB第三步：提取尾部",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 3, output_file, temp_dir))
        elif stage == 3:
            self.statusBar().showMessage(f"羊鞭AB第四步 {self.processed_pairs+1}/{self.total_pairs}: 核心合成")
            self.execute_stage(self.ffmpeg_cmd4, "羊鞭AB第四步：核心合成",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 4, output_file, temp_dir))
        elif stage == 4:
            self.statusBar().showMessage(f"羊鞭AB第五步 {self.processed_pairs+1}/{self.total_pairs}: 转换MKV")
            self.execute_stage(self.ffmpeg_cmd5, "羊鞭AB第五步：转换MKV",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 5, output_file, temp_dir))
        elif stage == 5:
            self.statusBar().showMessage(f"羊鞭AB第六步 {self.processed_pairs+1}/{self.total_pairs}: 提取视频流")
            self.execute_stage(self.ffmpeg_cmd6, "羊鞭AB第六步：提取视频流",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 6, output_file, temp_dir))
        elif stage == 6:
            self.statusBar().showMessage(f"羊鞭AB第七步 {self.processed_pairs+1}/{self.total_pairs}: 提取音频流")
            self.execute_stage(self.ffmpeg_cmd7, "羊鞭AB第七步：提取音频流",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 7, output_file, temp_dir))
        elif stage == 7:
            self.statusBar().showMessage(f"羊鞭AB第八步 {self.processed_pairs+1}/{self.total_pairs}: 最终合并")
            self.execute_stage(self.ffmpeg_cmd8, "羊鞭AB第八步：最终合并",
                             lambda success, msg: self.on_pojun_stage_finished(success, msg, 8, output_file, temp_dir))
        elif stage == 8:
            # 所有阶段完成
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
            self.cleanup_and_continue(temp_dir)

    def update_progress(self, progress_info):
        """更新进度条"""
        if "frame=" in progress_info:
            try:
                frame = int(progress_info.split("frame=")[1].split()[0])
                self.progress_bar.setValue(min(frame, 100))  # 直接使用帧数作为进度
            except:
                pass

    def execute_stage(self, cmd, stage_name, on_finished):
        """改进的执行处理阶段"""
        # 确保之前的线程已完成
        if hasattr(self, 'processing_thread') and self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.wait(5000)  # 等待最多5秒
            if self.processing_thread.isRunning():
                self.processing_thread.terminate()  # 强制终止
                self.processing_thread.wait()

        self.current_stage += 1
        self.progress_bar.setValue(int((self.current_stage-1)/self.total_stages*100))

        self.processing_thread = VideoProcessorThread(cmd)
        self.processing_thread.progress_updated.connect(
            lambda msg: (
                self.statusBar().showMessage(
                    f"{stage_name}中 {self.processed_pairs+1}/{self.total_pairs}: {msg}"
                ),
                self.update_progress(msg)
            )
        )
        self.processing_thread.processing_finished.connect(on_finished)
        self.processing_thread.start()

    def on_stage_finished(self, success, message, next_cmd, next_stage, output_file, temp_dir, partc_path=None):
        """处理阶段完成回调"""
        if not success:
            QMessageBox.warning(self, "警告", f"{message}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        if next_cmd:
            if next_stage == "第二阶段：主视频处理":
                # 第二阶段完成，执行第三阶段
                self.execute_stage(next_cmd, next_stage,
                                 lambda success, msg: self.on_stage_finished(
                                     success, msg, self.ffmpeg_cmd3, "第三阶段：提取片段", output_file, temp_dir, partc_path))
            elif next_stage == "第三阶段：提取片段":
                # 第三阶段完成，执行第四阶段
                self.execute_stage(next_cmd, next_stage,
                                 lambda success, msg: self.on_stage_finished(
                                     success, msg, self.ffmpeg_cmd4, "第四阶段：复杂合并", output_file, temp_dir, partc_path))
            elif next_stage == "第四阶段：复杂合并":
                # 第四阶段完成，执行第五阶段
                self.execute_stage(next_cmd, next_stage,
                                 lambda success, msg: self.on_stage_finished(
                                     success, msg, self.ffmpeg_cmd5, "第五阶段：MTV处理1", output_file, temp_dir, partc_path))
            elif next_stage == "第五阶段：MTV处理1":
                # 第五阶段完成，执行第六阶段
                self.execute_stage(next_cmd, next_stage,
                                 lambda success, msg: self.on_stage_finished(
                                     success, msg, self.ffmpeg_cmd6, "第六阶段：MTV处理2", output_file, temp_dir, partc_path))
            elif next_stage == "第六阶段：MTV处理2":
                # 第六阶段完成，执行第七阶段
                self.execute_stage(next_cmd, next_stage,
                                 lambda success, msg: self.on_stage_finished(
                                     success, msg, self.ffmpeg_cmd7, "第七阶段：最终合并", output_file, temp_dir, partc_path))
            elif next_stage == "第七阶段：最终合并":
                # 第七阶段完成
                self.execute_stage(next_cmd, next_stage,
                                 lambda success, msg: self.on_final_stage_finished(
                                     success, msg, output_file, temp_dir, partc_path))
        else:
            # 所有阶段完成
            self.processed_pairs += 1
            self.process_next_pair()

    def on_final_stage_finished(self, success, message, output_file, temp_dir, partc_path):
        """最终阶段完成回调"""
        import shutil
        import os

        if success:
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.warning(self, "警告", f"最终输出失败: {message}")

        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"清理临时文件失败: {str(e)}")

        self.processed_pairs += 1
        self.process_next_pair()

    def process_chengfeng_ab_mode(self, main_video, aux_video, output_file):
        """乘风AB模式处理 - 使用主视频比例和时长"""
        # 获取主视频时长和分辨率
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        try:
            width, height = self.get_video_dimensions(main_video)
        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取主视频分辨率失败: {str(e)}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()

        # 创建临时文件路径
        temp_paths = {
            "tempb": os.path.join(temp_dir, "tempb.mp4"),      # 辅助视频处理结果
            "tempa": os.path.join(temp_dir, "tempa.mp4"),      # 主视频处理结果
            "tempclip": os.path.join(temp_dir, "tempclip.mp4"), # 主视频片段
            "tempcombined": os.path.join(temp_dir, "tempcombined.mp4"), # 合并结果
            "tempcombined5": os.path.join(temp_dir, "tempcombined5.mkv"), # MKV格式
            "tempvideo": os.path.join(temp_dir, "tempvideo.h264"), # 视频轨道
            "tempaudio": os.path.join(temp_dir, "tempaudio.aac"), # 音频轨道
            "timecodes": os.path.join(temp_dir, "timecodes.txt"), # 时间码文件
            "temptack": os.path.join(temp_dir, "temptack.mkv") # 重新打包的MKV
        }

        self.statusBar().showMessage(f"乘风AB模式处理 {self.processed_pairs+1}/{self.total_pairs} - 主视频分辨率: {width}x{height}, 时长: {duration:.2f}秒")

        # 检查输入文件是否存在
        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "错误", f"辅助视频文件不存在: {aux_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 检测ffmpeg和相关工具路径
        def get_ffmpeg_path():
            # 优先检查ffmpeg目录中的ffmpeg.exe
            if os.path.exists(".\\ffmpeg\\ffmpeg.exe"):
                return ".\\ffmpeg\\ffmpeg.exe"
            elif os.path.exists("ffmpeg.exe"):
                return "ffmpeg.exe"
            else:
                return "ffmpeg"  # 系统PATH中的ffmpeg

        def get_mkv_path():
            # 检查mkvmerge是否可用，优先检查ffmpeg目录
            mkv_paths = [
                ".\\ffmpeg\\mkvmerge.exe",
                "mkvmerge.exe"
            ]
            for path in mkv_paths:
                if os.path.exists(path):
                    return path
            # 如果都不存在，返回None表示不可用
            return None

        def get_mkv3_path():
            # 检查mkvextract是否可用，优先检查ffmpeg目录
            # 注意：根据目录列表，mkvextract可能叫mkv3.exe
            mkv3_paths = [
                ".\\ffmpeg\\mkv3.exe",
                ".\\ffmpeg\\mkvextract.exe",
                "mkvextract.exe"
            ]
            for path in mkv3_paths:
                if os.path.exists(path):
                    return path
            # 如果都不存在，返回None表示不可用
            return None

        ffmpeg_exe = get_ffmpeg_path()
        mkv_exe = get_mkv_path()
        mkv3_exe = get_mkv3_path()

        # 检查mkvmerge工具是否可用
        if mkv_exe is None or mkv3_exe is None:
            QMessageBox.warning(self, "错误", "乘风AB模式需要mkvmerge工具，请确保ffmpeg目录中包含mkvmerge.exe和mkv3.exe")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 计算片段开始时间（主视频时长减去0.666秒）
        clip_start_time = max(0, duration - 0.666)

        # 第1步：处理辅助视频循环 - 使用主视频分辨率和时长
        self.ffmpeg_cmd_chengfeng_stage1 = [
            ffmpeg_exe, "-y", "-stream_loop", "-1", "-i", aux_video,
            "-t", str(duration),
            "-vf", f"scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1/1,setdar={width}/{height},fps=30",
            "-r", "30", "-map_metadata", "-1", "-c:v", "h264_nvenc",
            "-b:v", "8000k", "-maxrate", "8000k", "-bufsize", "8000k",
            "-x264opts", "nal-hrd=cbr", "-an",
            temp_paths["tempb"]
        ]

        # 执行第1步
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage1, "乘风AB第1步：辅助视频循环",
                         lambda success, msg: self.on_chengfeng_stage1_finished(success, msg, main_video, output_file, temp_dir, temp_paths, duration, width, height, clip_start_time, ffmpeg_exe, mkv_exe, mkv3_exe))

    def on_chengfeng_stage1_finished(self, success, message, main_video, output_file, temp_dir, temp_paths, duration, width, height, clip_start_time, ffmpeg_exe, mkv_exe, mkv3_exe):
        """乘风AB第1步完成回调"""
        import os
        if not success or not os.path.exists(temp_paths["tempb"]):
            QMessageBox.warning(self, "警告", f"乘风AB第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第2步：处理主视频 - 使用主视频分辨率
        self.ffmpeg_cmd_chengfeng_stage2 = [
            ffmpeg_exe, "-y", "-i", main_video,
            "-vf", f"setsar=1/1,setdar={width}/{height},scale={width}:{height}",
            "-c:v", "h264_nvenc", "-b:v", "6000k", "-maxrate:v", "6000k",
            "-r", "30", "-bufsize:v", "6000k", "-x264opts", "nal-hrd=cbr",
            "-c:a", "copy",
            temp_paths["tempa"]
        ]

        self.statusBar().showMessage(f"乘风AB第2步 {self.processed_pairs+1}/{self.total_pairs}: 处理主视频")
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage2, "乘风AB第2步：主视频处理",
                         lambda success, msg: self.on_chengfeng_stage2_finished(success, msg, main_video, output_file, temp_dir, temp_paths, duration, width, height, clip_start_time, ffmpeg_exe, mkv_exe, mkv3_exe))

    def on_chengfeng_stage2_finished(self, success, message, main_video, output_file, temp_dir, temp_paths, duration, width, height, clip_start_time, ffmpeg_exe, mkv_exe, mkv3_exe):
        """乘风AB第2步完成回调"""
        import os
        if not success or not os.path.exists(temp_paths["tempa"]):
            QMessageBox.warning(self, "警告", f"乘风AB第2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第3步：提取主视频片段 - 使用计算的开始时间
        self.ffmpeg_cmd_chengfeng_stage3 = [
            ffmpeg_exe, "-y", "-i", main_video,
            "-ss", str(clip_start_time), "-t", "0.666",
            "-vf", f"setsar=1/1,setdar={width}/{height},scale={width}:{height}",
            "-c:v", "h264_nvenc",
            temp_paths["tempclip"]
        ]

        self.statusBar().showMessage(f"乘风AB第3步 {self.processed_pairs+1}/{self.total_pairs}: 提取视频片段")
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage3, "乘风AB第3步：提取片段",
                         lambda success, msg: self.on_chengfeng_stage3_finished(success, msg, output_file, temp_dir, temp_paths, duration, width, height, ffmpeg_exe, mkv_exe, mkv3_exe))

    def on_chengfeng_stage3_finished(self, success, message, output_file, temp_dir, temp_paths, duration, width, height, ffmpeg_exe, mkv_exe, mkv3_exe):
        """乘风AB第3步完成回调"""
        import os
        if not success or not os.path.exists(temp_paths["tempclip"]):
            QMessageBox.warning(self, "警告", f"乘风AB第3步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第4步：合并视频 - 使用原来的逻辑但动态计算帧数
        # 计算辅助视频需要的帧数（主视频时长 * 30fps - 3帧）
        total_frames = int(duration * 30)  # 主视频总帧数
        end_frame = total_frames - 1  # 结束帧号（从0开始计数）

        print(f"🔧 乘风AB第4步 - 动态帧数计算: duration={duration}, total_frames={total_frames}, end_frame={end_frame}")
        print(f"🔧 修改后的代码正在运行！")

        # 调试：检查输入文件时长
        try:
            import subprocess
            for file_key, file_path in [("tempa", temp_paths["tempa"]), ("tempb", temp_paths["tempb"]), ("tempclip", temp_paths["tempclip"])]:
                probe_cmd = [ffmpeg_exe.replace("ffmpeg.exe", "ffprobe.exe"), "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", file_path]
                result = subprocess.run(probe_cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    file_duration = float(result.stdout.strip())
                    print(f"📁 {file_key}时长: {file_duration:.3f}秒")
        except Exception as e:
            print(f"⚠️ 无法检查输入文件时长: {e}")

        self.ffmpeg_cmd_chengfeng_stage4 = [
            ffmpeg_exe, "-y",
            "-i", temp_paths["tempa"],
            "-i", temp_paths["tempb"],
            "-i", temp_paths["tempclip"],
            "-filter_complex", f"[0:v]fps=30[a];[1:v]fps=30,select='between(n,3,{end_frame})'[b];[a][b]interleave[ab];[2:v]fps=30[c];[ab][c]concat",
            "-map", "0:a?",  # 映射主视频的音频
            "-c:v", "libx264", "-video_track_timescale", "1000000",
            "-g", "250", "-keyint_min", "250", "-x264-params", "bframes=0",
            "-rc", "crf", "-preset", "fast", "-movflags", "+faststart",
            "-c:a", "copy",  # 复制音频
            "-f", "mp4",
            temp_paths["tempcombined"]
        ]

        self.statusBar().showMessage(f"乘风AB第4步 {self.processed_pairs+1}/{self.total_pairs}: 合并视频")
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage4, "乘风AB第4步：合并视频",
                         lambda success, msg: self.on_chengfeng_stage4_finished(success, msg, output_file, temp_dir, temp_paths, duration, ffmpeg_exe, mkv_exe, mkv3_exe))

    def on_chengfeng_stage4_finished(self, success, message, output_file, temp_dir, temp_paths, duration, ffmpeg_exe, mkv_exe, mkv3_exe):
        """乘风AB第4步完成回调"""
        import os
        if not success or not os.path.exists(temp_paths["tempcombined"]):
            QMessageBox.warning(self, "警告", f"乘风AB第4步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第5步：转换为MKV格式
        self.ffmpeg_cmd_chengfeng_stage5 = [
            mkv_exe, "--ui-language", "zh_CN", "--priority", "lower",
            "--output", temp_paths["tempcombined5"],
            "--language", "0:und",
            temp_paths["tempcombined"],
            "--track-order", "0:0,0:1"
        ]

        self.statusBar().showMessage(f"乘风AB第5步 {self.processed_pairs+1}/{self.total_pairs}: 转换MKV格式")
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage5, "乘风AB第5步：转换MKV",
                         lambda success, msg: self.on_chengfeng_stage5_finished(success, msg, output_file, temp_dir, temp_paths, duration, ffmpeg_exe, mkv_exe, mkv3_exe))

    def on_chengfeng_stage5_finished(self, success, message, output_file, temp_dir, temp_paths, duration, ffmpeg_exe, mkv_exe, mkv3_exe):
        """乘风AB第5步完成回调"""
        import os
        if not success or not os.path.exists(temp_paths["tempcombined5"]):
            QMessageBox.warning(self, "警告", f"乘风AB第5步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第6步：提取轨道
        self.ffmpeg_cmd_chengfeng_stage6 = [
            mkv3_exe, "tracks", temp_paths["tempcombined5"],
            f"0:{temp_paths['tempvideo']}",
            f"1:{temp_paths['tempaudio']}"
        ]

        self.statusBar().showMessage(f"乘风AB第6步 {self.processed_pairs+1}/{self.total_pairs}: 提取轨道")
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage6, "乘风AB第6步：提取轨道",
                         lambda success, msg: self.on_chengfeng_stage6_finished(success, msg, output_file, temp_dir, temp_paths, duration, ffmpeg_exe, mkv_exe, mkv3_exe))

    def on_chengfeng_stage6_finished(self, success, message, output_file, temp_dir, temp_paths, duration, ffmpeg_exe, mkv_exe, mkv3_exe):
        """乘风AB第6步完成回调"""
        import os
        import psutil
        if not success or not os.path.exists(temp_paths["tempvideo"]):
            QMessageBox.warning(self, "警告", f"乘风AB第6步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 验证输入文件
        error_msgs = []
        if not os.path.exists(temp_paths["tempvideo"]):
            error_msgs.append(f"视频轨道文件不存在: {temp_paths['tempvideo']}")
        if not os.path.exists(temp_paths["tempaudio"]):
            error_msgs.append(f"音频轨道文件不存在: {temp_paths['tempaudio']}")
        
        # 检查文件大小
        if os.path.exists(temp_paths["tempvideo"]) and os.path.getsize(temp_paths["tempvideo"]) == 0:
            error_msgs.append("视频轨道文件为空")
        if os.path.exists(temp_paths["tempaudio"]) and os.path.getsize(temp_paths["tempaudio"]) == 0:
            error_msgs.append("音频轨道文件为空")

        if error_msgs:
            QMessageBox.warning(self, "错误", "\n".join(error_msgs))
            self.cleanup_and_continue(temp_dir)
            return

        # 检查系统资源
        mem = psutil.virtual_memory()
        if mem.percent > 90:
            QMessageBox.warning(self, "警告", f"系统内存不足 (使用率: {mem.percent}%)")
            self.cleanup_and_continue(temp_dir)
            return

        # 第7步：重新打包 (包含时间码文件)
        try:
            # 记录命令执行环境
            with open("mkvmerge_log.txt", "a", encoding='utf-8') as log:
                log.write(f"\n{'='*40}\n")
                log.write(f"执行时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                log.write(f"工作目录: {os.getcwd()}\n")
                log.write(f"视频文件大小: {os.path.getsize(temp_paths['tempvideo'])} bytes\n")
                log.write(f"音频文件大小: {os.path.getsize(temp_paths['tempaudio'])} bytes\n")
                log.write(f"内存状态: {mem.percent}% 使用中\n")

            # 生成简化的时间码文件 - 避免TimecodeDelay错误但保持时长控制
            main_frame_count = int(duration * 30)
            # 只生成实际需要的帧数，不多生成
            frame_count = main_frame_count + 50  # 少量缓冲

            with open(temp_paths["timecodes"], 'w', encoding='utf-8') as f:
                f.write("# timecode format v2\n")
                # 使用精确的30fps时间戳：1000ms/30fps = 33.333333ms
                for i in range(frame_count):
                    timestamp_ms = i * 1000.0 / 30.0  # 精确的30fps计算
                    f.write(f"{timestamp_ms:.6f}\n")  # 保留6位小数精度

            print(f"🔧 生成简化时间码文件: {frame_count}个时间戳，确保时长{duration:.2f}秒")

            # 调试信息：检查文件状态和时长
            print(f"📁 时间码文件: {temp_paths['timecodes']}")
            print(f"📁 视频文件: {temp_paths['tempvideo']} ({os.path.getsize(temp_paths['tempvideo'])/1024/1024:.1f}MB)")
            print(f"📁 音频文件: {temp_paths['tempaudio']} ({os.path.getsize(temp_paths['tempaudio'])/1024/1024:.1f}MB)")
            print(f"🎬 主视频时长: {duration:.3f}秒")
            print(f"🎬 预期帧数: {main_frame_count}, 生成时间戳: {frame_count}")
            # 精确计算时间戳范围
            last_timestamp_ms = (frame_count - 1) * 1000.0 / 30.0
            print(f"🎬 时间戳范围: 0ms ~ {last_timestamp_ms:.3f}ms ({last_timestamp_ms/1000:.3f}秒)")

            # 检查时间戳是否覆盖预期时长
            expected_duration_ms = duration * 1000
            timestamp_duration_ms = last_timestamp_ms
            if timestamp_duration_ms < expected_duration_ms:
                print(f"⚠️  警告：时间戳时长({timestamp_duration_ms/1000:.3f}s) < 预期时长({duration:.3f}s)")
            else:
                print(f"✅ 时间戳覆盖充足")

            # 构建命令 - 使用简化的时间码文件
            self.ffmpeg_cmd_chengfeng_stage7 = [
                mkv_exe, "-o", temp_paths["temptack"],
                "--timestamps", f"0:{temp_paths['timecodes']}",
                temp_paths["tempvideo"],
                "--audio-tracks", "0",
                temp_paths["tempaudio"]
            ]
        except Exception as e:
            QMessageBox.warning(self, "错误", f"准备mkvmerge命令失败: {str(e)}")
            self.cleanup_and_continue(temp_dir)
            return

        self.statusBar().showMessage(f"乘风AB第7步 {self.processed_pairs+1}/{self.total_pairs}: 重新打包")

        # 直接执行mkvmerge命令并捕获详细输出
        try:
            import subprocess
            result = subprocess.run(
                self.ffmpeg_cmd_chengfeng_stage7,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            # 记录详细的mkvmerge输出
            with open("mkvmerge_detailed_log.txt", "a", encoding='utf-8') as log:
                log.write(f"\n{'='*50}\n")
                log.write(f"执行时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                log.write(f"命令: {' '.join(self.ffmpeg_cmd_chengfeng_stage7)}\n")
                log.write(f"返回码: {result.returncode}\n")
                log.write(f"标准输出:\n{result.stdout}\n")
                log.write(f"错误输出:\n{result.stderr}\n")

            # 检查输出文件是否成功生成
            if os.path.exists(temp_paths["temptack"]) and os.path.getsize(temp_paths["temptack"]) > 0:
                # 文件生成成功，即使返回码是1（警告）也视为成功
                if result.returncode == 1 and "Warning:" in result.stdout and "Progress: 100%" in result.stdout:
                    print(f"🔧 mkvmerge完成但有警告 (返回码: {result.returncode})，文件已成功生成")
                    self.on_chengfeng_stage7_finished(True, "处理完成（有警告）", output_file, temp_dir, temp_paths, ffmpeg_exe, mkv_exe)
                elif result.returncode == 0:
                    self.on_chengfeng_stage7_finished(True, "处理完成", output_file, temp_dir, temp_paths, ffmpeg_exe, mkv_exe)
                else:
                    # 真正的错误
                    error_msg = f"mkvmerge失败 (返回码: {result.returncode})\n标准输出: {result.stdout}\n错误输出: {result.stderr}"
                    self.on_chengfeng_stage7_finished(False, error_msg, output_file, temp_dir, temp_paths, ffmpeg_exe, mkv_exe)
            else:
                # 文件未生成或为空，肯定是失败
                error_msg = f"mkvmerge未生成输出文件 (返回码: {result.returncode})\n标准输出: {result.stdout}\n错误输出: {result.stderr}"
                self.on_chengfeng_stage7_finished(False, error_msg, output_file, temp_dir, temp_paths, ffmpeg_exe, mkv_exe)

        except Exception as e:
            error_msg = f"执行mkvmerge命令异常: {str(e)}"
            self.on_chengfeng_stage7_finished(False, error_msg, output_file, temp_dir, temp_paths, ffmpeg_exe, mkv_exe)

    def on_chengfeng_stage7_finished(self, success, message, output_file, temp_dir, temp_paths, ffmpeg_exe, mkv_exe):
        """乘风AB第7步完成回调"""
        import os
        import subprocess
        import psutil
        
        if not success or not os.path.exists(temp_paths["temptack"]):
            # 记录详细系统状态
            error_log = [
                f"=== 乘风AB第7步错误报告 ===",
                f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"错误代码: 3221225477 (访问冲突)",
                f"错误信息: {message}",
                f"临时目录: {temp_dir}",
                "",
                "系统资源状态:",
                f"内存使用率: {psutil.virtual_memory().percent}%",
                f"CPU使用率: {psutil.cpu_percent()}%",
                f"磁盘空间: {psutil.disk_usage('/').free / (1024*1024):.1f}MB 可用",
                "",
                "输入文件状态:"
            ]
            
            # 检查输入文件状态
            for file_type in ["tempvideo", "tempaudio", "temptack"]:
                file_path = temp_paths[file_type]
                if os.path.exists(file_path):
                    file_stats = [
                        f"- {file_type}:",
                        f"  路径: {file_path}",
                        f"  大小: {os.path.getsize(file_path) / (1024*1024):.2f}MB",
                        f"  修改时间: {time.ctime(os.path.getmtime(file_path))}"
                    ]
                    error_log.extend(file_stats)
                else:
                    error_log.append(f"- {file_type}: 文件不存在")
            
            # 检查mkvmerge版本
            try:
                version_cmd = [mkv_exe, "--version"]
                version_output = subprocess.run(version_cmd, capture_output=True, text=True).stdout
                error_log.extend([
                    "",
                    "mkvmerge版本信息:",
                    version_output.split('\n')[0]  # 只取第一行版本信息
                ])
            except Exception as e:
                error_log.extend([
                    "",
                    "mkvmerge版本检查失败:",
                    str(e)
                ])
            
            # 写入错误日志
            with open("chengfeng_error.log", "a", encoding="utf-8") as f:
                f.write("\n".join(error_log) + "\n\n")
            
            # 显示精简错误信息
            QMessageBox.warning(self, "错误", 
                "乘风AB第7步失败\n\n" +
                f"错误代码: 3221225477\n" +
                f"详情请查看日志文件: chengfeng_error.log")
                
            self.cleanup_and_continue(temp_dir)
            return

        # 第8步：跳过有问题的MKV文件，直接处理h264和aac
        self.ffmpeg_cmd_chengfeng_stage8 = [
            ffmpeg_exe, "-y",
            "-i", temp_paths["tempvideo"],  # 直接使用h264文件
            "-i", temp_paths["tempaudio"],  # 直接使用aac文件
            "-map", "0:v:0",  # 视频流
            "-map", "1:a:0",  # 音频流
            "-f", "mp4",
            "-c:v", "copy",   # 直接复制
            "-c:a", "copy",   # 直接复制
            output_file
        ]

        self.statusBar().showMessage(f"乘风AB第8步 {self.processed_pairs+1}/{self.total_pairs}: 最终输出")
        self.execute_stage(self.ffmpeg_cmd_chengfeng_stage8, "乘风AB第8步：最终输出",
                         lambda success, msg: self.on_chengfeng_stage8_finished(success, msg, output_file, temp_dir))

    def on_chengfeng_stage8_finished(self, success, message, output_file, temp_dir):
        """乘风AB第8步完成回调"""
        import shutil
        import os

        if success and os.path.exists(output_file):
            self.statusBar().showMessage(f"乘风AB完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.warning(self, "警告", f"乘风AB第8步失败: {message}")

        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
        except Exception as e:
            print(f"清理临时文件失败: {str(e)}")

        self.processed_pairs += 1
        self.process_next_pair()






    def process_kaitian_ab_mode(self, main_video, aux_video, output_file):
        """牛牛AB模式处理 - 使用无尽AB的6步FFmpeg命令序列"""
        # 获取主视频属性
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取副视频分辨率（无尽AB使用副视频的分辨率）
        aux_resolution = self.get_video_resolution(aux_video)
        if not aux_resolution:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        try:
            width, height = aux_resolution.split('x')
            aux_width, aux_height = int(width), int(height)
        except:
            QMessageBox.warning(self, "警告", f"无法解析副视频分辨率: {aux_resolution}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        import random
        import string
        temp_dir = tempfile.mkdtemp()

        # 创建临时文件路径 - 按照无尽AB的命名方式
        left_1 = os.path.join(temp_dir, "left_1.mp4")
        left_2 = os.path.join(temp_dir, "left_2.mp4")
        right_1 = os.path.join(temp_dir, "right_1.mp4")
        left_3 = os.path.join(temp_dir, "left_3.mp4")

        self.statusBar().showMessage(f"无尽AB模式处理 {self.processed_pairs+1}/{self.total_pairs} - 使用无尽AB算法")

        # 检查输入文件是否存在
        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "错误", f"辅助视频文件不存在: {aux_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        if not os.path.exists(main_video):
            QMessageBox.warning(self, "错误", f"主视频文件不存在: {main_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 无尽AB第1步：视频去重处理（处理副视频）
        duration_str = f"{duration:.6f}"
        self.ffmpeg_cmd_kaitian_stage1 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", aux_video,
            "-vf", "fps=60",
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-c:a", "copy", left_1
        ]

        # 执行第1步
        self.execute_stage(self.ffmpeg_cmd_kaitian_stage1, "无尽AB第1步：视频去重处理",
                         lambda success, msg: self.on_kaitian_stage1_finished(success, msg, main_video, aux_video, output_file, temp_dir, aux_width, aux_height, duration, duration_str, left_1, left_2, right_1, left_3))

    def on_kaitian_stage1_finished(self, success, message, main_video, aux_video, output_file, temp_dir, aux_width, aux_height, duration, duration_str, left_1, left_2, right_1, left_3):
        """无尽AB第1步完成回调"""
        import os
        if not success or not os.path.exists(left_1):
            QMessageBox.warning(self, "警告", f"无尽AB第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 无尽AB第2步：视频二次处理（提取0.333秒片段）
        vf3 = f'scale={aux_width}:{aux_height}:force_original_aspect_ratio=decrease,pad={aux_width}:{aux_height}:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60'
        self.ffmpeg_cmd_kaitian_stage2 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", left_1,
            "-ss", "00:00:00", "-t", "0.333",
            "-vf", vf3,
            "-c:v", "libx264", "-preset", "medium", "-x264-params", "crf=23",
            "-profile:v", "main", "-pix_fmt", "yuv420p", "-movflags", "+faststart",
            "-flags", "+cgop", "-tune", "ssim", "-an", left_2
        ]

        self.statusBar().showMessage(f"无尽AB第2步 {self.processed_pairs+1}/{self.total_pairs}: 视频二次处理")
        self.execute_stage(self.ffmpeg_cmd_kaitian_stage2, "无尽AB第2步：视频二次处理",
                         lambda success, msg: self.on_kaitian_stage2_finished(success, msg, main_video, output_file, temp_dir, aux_width, aux_height, duration, duration_str, left_1, left_2, right_1, left_3))

    def on_kaitian_stage2_finished(self, success, message, main_video, output_file, temp_dir, main_width, main_height, duration, path_processed_b, path_processed_a, path_clip_a, path_combined_video, path_intermediate_mkv, total_frames):
        """牛牛AB第2步完成回调"""
        import os
        if not success or not os.path.exists(path_processed_a):
            QMessageBox.warning(self, "警告", f"牛牛AB第2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第3步：裁剪A视频尾部片段 - 完全使用牛牛AB的参数
        CLIP_DURATION_S = 0.666  # 牛牛AB中的常量
        THREADS = 12  # 牛牛AB中的线程数
        start_ss = max(0, duration - CLIP_DURATION_S)
        self.ffmpeg_cmd_kaitian_stage3 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", main_video,
            "-ss", f"{start_ss:.3f}", "-t", f"{CLIP_DURATION_S}",
            "-vf", f"setsar=1/1,setdar={main_width}/{main_height},scale={main_width}:{main_height}",
            "-c:v", "libx264", "-an", path_clip_a, "-threads", str(THREADS)
        ]

        self.statusBar().showMessage(f"牛牛AB第3步 {self.processed_pairs+1}/{self.total_pairs}: 裁剪A视频")
        self.execute_stage(self.ffmpeg_cmd_kaitian_stage3, "牛牛AB第3步：裁剪A视频",
                         lambda success, msg: self.on_kaitian_stage3_finished(success, msg, output_file, temp_dir, main_width, main_height, duration, path_processed_b, path_processed_a, path_clip_a, path_combined_video, path_intermediate_mkv, total_frames))

    def on_kaitian_stage3_finished(self, success, message, output_file, temp_dir, main_width, main_height, duration, path_processed_b, path_processed_a, path_clip_a, path_combined_video, path_intermediate_mkv, total_frames):
        """牛牛AB第3步完成回调"""
        import os
        if not success or not os.path.exists(path_clip_a):
            QMessageBox.warning(self, "警告", f"牛牛AB第3步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第4步：合成视频 - 完全使用牛牛AB的filter_complex
        THREADS = 12  # 牛牛AB中的线程数
        select_filter = f"select='between(n,3,{total_frames})'"
        filter_complex = f"[0:v]fps=30[a];[1:v]fps=30,{select_filter}[b];[a][b]interleave[ab];[2:v]fps=30[c];[ab][c]concat"
        self.ffmpeg_cmd_kaitian_stage4 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", path_processed_a, "-i", path_processed_b, "-i", path_clip_a,
            "-filter_complex", filter_complex,
            "-c:v", "libx264", "-video_track_timescale", "1000000", "-g", "250", "-keyint_min", "250",
            "-x264-params", "bframes=0", "-rc", "crf", "-preset", "fast", "-movflags", "+faststart",
            "-f", "mp4", path_combined_video, "-threads", str(THREADS)
        ]

        self.statusBar().showMessage(f"牛牛AB第4步 {self.processed_pairs+1}/{self.total_pairs}: 合成视频")
        self.execute_stage(self.ffmpeg_cmd_kaitian_stage4, "牛牛AB第4步：合成视频",
                         lambda success, msg: self.on_kaitian_stage4_finished(success, msg, output_file, temp_dir, path_processed_b, path_processed_a, path_combined_video, path_intermediate_mkv))

    def on_kaitian_stage4_finished(self, success, message, output_file, temp_dir, path_processed_b, path_processed_a, path_combined_video, path_intermediate_mkv):
        """牛牛AB第4步完成回调"""
        import os
        if not success or not os.path.exists(path_combined_video):
            QMessageBox.warning(self, "警告", f"牛牛AB第4步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第5步：创建MKV中间文件 - 使用牛牛AB的参数
        self.ffmpeg_cmd_kaitian_stage5 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", path_combined_video, "-i", path_processed_a, "-i", path_processed_b,
            "-map", "2:v:0", "-map", "0:v:0", "-map", "2:v:0", "-map", "1:a:0",
            "-c", "copy", "-disposition:v:0", "0", "-disposition:v:1", "default", "-disposition:v:2", "0",
            "-metadata:s:v:1", "language=eng", "-shortest", path_intermediate_mkv
        ]

        self.statusBar().showMessage(f"牛牛AB第5步 {self.processed_pairs+1}/{self.total_pairs}: 创建MKV中间文件")
        self.execute_stage(self.ffmpeg_cmd_kaitian_stage5, "牛牛AB第5步：创建MKV中间文件",
                         lambda success, msg: self.on_kaitian_stage5_finished(success, msg, output_file, temp_dir, path_intermediate_mkv))

    def get_video_fps(self, video_path):
        """获取视频帧率"""
        try:
            command = [
                ".\\ffmpeg\\ffprobe.exe", "-v", "error", "-select_streams", "v:0",
                "-show_entries", "stream=r_frame_rate", "-of", "csv=p=0", video_path
            ]
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            result = subprocess.run(command, capture_output=True, text=True,
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            if result.returncode == 0:
                fps_str = result.stdout.strip()
                if '/' in fps_str:
                    # 处理分数形式的帧率，如 "30000/1001"
                    numerator, denominator = fps_str.split('/')
                    fps = float(numerator) / float(denominator)
                else:
                    fps = float(fps_str)
                return round(fps, 2)
        except Exception as e:
            print(f"获取帧率失败: {e}")
        return None

    def get_frame_count_simple(self, video_path):
        """获取视频帧数 - 多种方法尝试"""
        # 方法1：尝试获取nb_read_frames
        try:
            command = [
                ".\\ffmpeg\\ffprobe.exe", "-v", "error", "-select_streams", "v:0",
                "-count_frames", "-show_entries", "stream=nb_read_frames",
                "-csv=p=0", video_path
            ]
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            result = subprocess.run(command, capture_output=True, text=True,
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            if result.returncode == 0 and result.stdout.strip():
                frame_count = int(result.stdout.strip())
                if frame_count > 0:
                    return frame_count
        except Exception as e:
            print(f"方法1获取帧数失败: {e}")

        # 方法2：通过时长和帧率计算
        try:
            duration = self.get_video_duration(video_path)
            fps = self.get_video_fps(video_path)
            if duration and fps:
                calculated_frames = int(duration * fps)
                if calculated_frames > 0:
                    print(f"通过时长({duration}s)和帧率({fps}fps)计算帧数: {calculated_frames}")
                    return calculated_frames
        except Exception as e:
            print(f"方法2计算帧数失败: {e}")

        # 方法3：使用ffmpeg直接统计
        try:
            command = [
                ".\\ffmpeg\\ffmpeg.exe", "-i", video_path, "-map", "0:v:0",
                "-c", "copy", "-f", "null", "-"
            ]
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            result = subprocess.run(command, capture_output=True, text=True,
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            # 从stderr中提取frame信息
            if "frame=" in result.stderr:
                lines = result.stderr.split('\n')
                for line in reversed(lines):
                    if "frame=" in line:
                        frame_part = line.split("frame=")[1].split()[0]
                        frame_count = int(frame_part)
                        if frame_count > 0:
                            print(f"通过ffmpeg统计获取帧数: {frame_count}")
                            return frame_count
        except Exception as e:
            print(f"方法3统计帧数失败: {e}")

        print(f"所有方法都无法获取帧数: {video_path}")
        return None

    def generate_timestamps_file(self, frame_count, timestamps_path):
        """生成时间戳文件"""
        try:
            with open(timestamps_path, 'w') as f:
                f.write("# timestamp format v2\n")
                for i in range(frame_count):
                    timestamp_ms = i * 33.333  # 30fps = 33.333ms per frame
                    f.write(f"{timestamp_ms:.3f}\n")
            return True
        except Exception as e:
            print(f"生成时间戳文件失败: {e}")
            return False

    def on_kaitian_stage5_finished(self, success, message, output_file, temp_dir, path_intermediate_mkv):
        """牛牛AB第5步完成回调"""
        import os
        if not success or not os.path.exists(path_intermediate_mkv):
            QMessageBox.warning(self, "警告", f"牛牛AB第5步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第6步：最终封装 - 使用牛牛AB的参数
        self.ffmpeg_cmd_kaitian_stage6 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", path_intermediate_mkv,
            "-map", "0", "-c", "copy", "-f", "mov", output_file
        ]

        self.statusBar().showMessage(f"牛牛AB第6步 {self.processed_pairs+1}/{self.total_pairs}: 最终封装")
        self.execute_stage(self.ffmpeg_cmd_kaitian_stage6, "牛牛AB第6步：最终封装",
                         lambda success, msg: self.on_kaitian_stage6_finished(success, msg, output_file, temp_dir))

    def check_mkv_tracks(self, mkv_file):
        """检查MKV文件的轨道信息"""
        try:
            import subprocess
            command = [".\\ffmpeg\\ffprobe.exe", "-v", "quiet", "-print_format", "json", "-show_streams", mkv_file]
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            result = subprocess.run(command, capture_output=True, text=True,
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                print(f"MKV文件轨道信息: {len(data.get('streams', []))} 个轨道")
                for i, stream in enumerate(data.get('streams', [])):
                    print(f"轨道 {i}: {stream.get('codec_type', 'unknown')} - {stream.get('codec_name', 'unknown')}")
        except Exception as e:
            print(f"检查MKV轨道失败: {e}")

    def has_audio_track(self, mkv_file):
        """检查MKV文件是否有音频轨道"""
        try:
            import subprocess
            command = [".\\ffmpeg\\ffprobe.exe", "-v", "quiet", "-select_streams", "a", "-show_entries", "stream=index", "-of", "csv=p=0", mkv_file]
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            result = subprocess.run(command, capture_output=True, text=True,
                                  startupinfo=startupinfo, creationflags=subprocess.CREATE_NO_WINDOW)
            return result.returncode == 0 and result.stdout.strip()
        except:
            return False

    def on_kaitian_stage6_finished(self, success, message, output_file, temp_dir):
        """牛牛AB第6步完成回调 - 最终完成"""
        import shutil
        import os

        if success:
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.warning(self, "警告", f"牛牛AB最终封装失败: {message}")

        # 清理临时文件并继续下一个
        self.cleanup_and_continue(temp_dir)

    def process_angela_mode(self, main_video, aux_video, output_file):
        """安琪拉模式处理 - 5步FFmpeg命令序列"""
        # 获取主视频时长
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取主视频分辨率
        resolution = self.get_video_resolution(main_video)
        if not resolution:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 解析主视频分辨率
        try:
            width, height = resolution.split('x')
            main_width, main_height = int(width), int(height)
        except:
            QMessageBox.warning(self, "警告", f"无法解析主视频分辨率: {resolution}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()

        # 创建临时文件路径
        temp_crop_path = os.path.join(temp_dir, "temp_crop.mp4")
        temp_a_path = os.path.join(temp_dir, "temp_a.mp4")
        temp_bg_processed_path = os.path.join(temp_dir, "temp_bg_processed.mp4")
        temp_bg_with_header_path = os.path.join(temp_dir, "temp_bg_with_header.mp4")

        self.statusBar().showMessage(f"安琪拉模式处理 {self.processed_pairs+1}/{self.total_pairs}")

        # 检查输入文件是否存在
        if not os.path.exists(aux_video):
            QMessageBox.warning(self, "错误", f"辅助视频文件不存在: {aux_video}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 第1步：裁剪主视频头部片段 (0.333秒)
        self.ffmpeg_cmd_angela_stage1 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", main_video,
            "-ss", "00:00:00", "-t", "0.333",
            "-vf", f"scale={main_width}:{main_height},setsar=1:1",
            "-c:v", "libx264", "-preset", "medium", "-threads", "0",
            temp_crop_path
        ]

        # 第2步：处理主视频 (完整时长)
        self.ffmpeg_cmd_angela_stage2 = [
            ".\\ffmpeg\\ffmpeg.exe", "-hwaccel", "none", "-y", "-i", main_video,
            "-t", str(duration),
            "-vf", f"scale={main_width}:{main_height},setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "medium",
            "-b:v", "15000k", "-maxrate:v", "15000k", "-bufsize:v", "15000k",
            "-c:a", "copy", "-threads", "0",
            temp_a_path
        ]

        # 第3步：处理背景视频 (循环播放)
        self.ffmpeg_cmd_angela_stage3 = [
            ".\\ffmpeg\\ffmpeg.exe", "-hwaccel", "none", "-y",
            "-stream_loop", "-1", "-i", aux_video,
            "-t", str(duration),
            "-vf", f"scale={main_width}:{main_height},setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "medium",
            "-b:v", "15000k", "-maxrate:v", "15000k", "-bufsize:v", "15000k",
            "-an", "-threads", "0",
            temp_bg_processed_path
        ]

        # 第4步：合并头部片段和背景视频
        trim_duration = duration - 0.333
        self.ffmpeg_cmd_angela_stage4 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y",
            "-i", temp_crop_path, "-i", temp_bg_processed_path,
            "-filter_complex",
            f"[0:v]trim=duration=0.333,setpts=N/FRAME_RATE/TB[v0];[1:v]trim=duration={trim_duration:.2f},setpts=N/FRAME_RATE/TB[v1];[v0][v1]concat=n=2:v=1:a=0,fps=60[v]",
            "-map", "[v]", "-c:v", "libx264", "-preset", "medium",
            "-b:v", "15000k", "-maxrate:v", "15000k", "-bufsize:v", "15000k",
            "-r", "60", "-an", "-threads", "0",
            temp_bg_with_header_path
        ]

        # 第5步：最终交替合成
        self.ffmpeg_cmd_angela_stage5 = [
            ".\\ffmpeg\\ffmpeg.exe", "-hwaccel", "none", "-y",
            "-i", temp_a_path, "-i", temp_bg_with_header_path,
            "-filter_complex",
            "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
            "-map", "[v]", "-c:v", "libx264", "-preset", "medium",
            "-vsync", "vfr", "-b:v", "15000k", "-maxrate", "15000k", "-minrate", "15000k",
            "-bufsize", "15000k", "-rc", "cbr", "-bf", "0",
            "-video_track_timescale", "1000000",
            "-map", "0:a?", "-c:a", "copy", "-t", str(duration), "-threads", "0",
            output_file
        ]

        # 调试信息：打印FFmpeg命令
        print(f"安琪拉模式第一阶段命令: {' '.join(self.ffmpeg_cmd_angela_stage1)}")

        # 执行5阶段处理
        self.execute_stage(self.ffmpeg_cmd_angela_stage1, "安琪拉第一阶段：裁剪头部",
                         lambda success, msg: self.on_angela_stage1_finished(success, msg, temp_dir, output_file))

    def on_angela_stage1_finished(self, success, message, temp_dir, output_file):
        """安琪拉第一阶段完成回调"""
        import os
        if not success:
            QMessageBox.critical(self, "错误", f"安琪拉第一阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第二阶段
        self.execute_stage(self.ffmpeg_cmd_angela_stage2, "安琪拉第二阶段：处理主视频",
                         lambda success, msg: self.on_angela_stage2_finished(success, msg, temp_dir, output_file))

    def on_angela_stage2_finished(self, success, message, temp_dir, output_file):
        """安琪拉第二阶段完成回调"""
        if not success:
            QMessageBox.critical(self, "错误", f"安琪拉第二阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第三阶段
        self.execute_stage(self.ffmpeg_cmd_angela_stage3, "安琪拉第三阶段：处理背景",
                         lambda success, msg: self.on_angela_stage3_finished(success, msg, temp_dir, output_file))

    def on_angela_stage3_finished(self, success, message, temp_dir, output_file):
        """安琪拉第三阶段完成回调"""
        if not success:
            QMessageBox.critical(self, "错误", f"安琪拉第三阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第四阶段
        self.execute_stage(self.ffmpeg_cmd_angela_stage4, "安琪拉第四阶段：合并头部",
                         lambda success, msg: self.on_angela_stage4_finished(success, msg, temp_dir, output_file))

    def on_angela_stage4_finished(self, success, message, temp_dir, output_file):
        """安琪拉第四阶段完成回调"""
        if not success:
            QMessageBox.critical(self, "错误", f"安琪拉第四阶段失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 执行第五阶段
        self.execute_stage(self.ffmpeg_cmd_angela_stage5, "安琪拉第五阶段：最终合成",
                         lambda success, msg: self.on_angela_stage5_finished(success, msg, temp_dir, output_file))

    def on_angela_stage5_finished(self, success, message, temp_dir, output_file):
        """安琪拉第五阶段完成回调"""
        import shutil
        import os

        if success:
            self.statusBar().showMessage(f"完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            QMessageBox.critical(self, "错误", f"安琪拉第五阶段失败: {message}")

        # 清理临时文件并继续下一个
        self.cleanup_and_continue(temp_dir)

    def process_dual_stream_mode(self, main_video_path, interference_video_path, output_path):
        """双流模式处理 - capture目录辅助帧，source目录主视频帧，final目录合并帧"""
        # 获取主视频信息
        duration = self.get_video_duration(main_video_path)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取主视频分辨率
        resolution = self.get_video_resolution(main_video_path)
        if not resolution:
            QMessageBox.warning(self, "错误", f"无法获取主视频分辨率: {main_video_path}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 解析主视频分辨率
        try:
            width, height = resolution.split('x')
            target_width, target_height = int(width), int(height)
        except:
            QMessageBox.warning(self, "警告", f"无法解析主视频分辨率: {resolution}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取主视频帧率
        fps = self.get_video_fps(main_video_path)
        target_fps = fps if fps else 30  # 默认30fps

        print(f"主视频分辨率: {target_width}x{target_height}")
        print(f"主视频帧率: {target_fps}")

        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()

        # 创建临时目录结构
        capture_dir = os.path.join(temp_dir, "capture")  # 辅助视频帧
        source_dir = os.path.join(temp_dir, "source")    # 主视频帧
        final_dir = os.path.join(temp_dir, "final")      # 合并帧
        os.makedirs(capture_dir, exist_ok=True)
        os.makedirs(source_dir, exist_ok=True)
        os.makedirs(final_dir, exist_ok=True)

        self.statusBar().showMessage(f"双流模式处理 {self.processed_pairs+1}/{self.total_pairs}")

        # 检查输入文件是否存在
        if not os.path.exists(interference_video_path):
            QMessageBox.warning(self, "错误", f"干扰视频文件不存在: {interference_video_path}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 双飞AB算法 - 第1步：提取主视频音频
        main_name = os.path.splitext(os.path.basename(main_video_path))[0]
        aac_file = os.path.join(temp_dir, f"{main_name}.aac")
        self.ffmpeg_cmd_dual_stage1 = [
            '.\\ffmpeg\\ffmpeg.exe', '-y', '-i', main_video_path,
            '-vn', '-c:a', 'copy',
            aac_file
        ]

        # 执行第1步
        self.statusBar().showMessage(f"双流第1步 {self.processed_pairs+1}/{self.total_pairs}: 提取主视频音频")
        self.execute_stage(self.ffmpeg_cmd_dual_stage1, "双流第1步：提取主视频音频",
                         lambda success, msg: self.on_dual_stage1_finished_new(success, msg, main_video_path, interference_video_path, output_path, temp_dir, target_width, target_height, aac_file, main_name))

    def on_dual_stage1_finished_new(self, success, message, main_video_path, interference_video_path, output_path, temp_dir, target_width, target_height, aac_file, main_name):
        """双流第1步完成回调（双飞AB算法）"""
        import os

        if not success:
            QMessageBox.warning(self, "警告", f"双流第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 检查音频文件是否成功提取
        if not os.path.exists(aac_file):
            QMessageBox.warning(self, "警告", "双流第1步：未能提取到主视频音频")
            self.cleanup_and_continue(temp_dir)
            return

        # 第2步：生成主视频无声60帧版
        track1 = os.path.join(temp_dir, f"{main_name}_track1.mp4")
        self.ffmpeg_cmd_dual_stage2 = [
            '.\\ffmpeg\\ffmpeg.exe', '-y', '-i', main_video_path,
            '-an', '-vf', f'fps=60,scale={target_width}:{target_height},setsar=1/1',
            '-c:v', 'libx264',
            track1
        ]

        self.statusBar().showMessage(f"双流第2步 {self.processed_pairs+1}/{self.total_pairs}: 生成主视频无声60帧版")
        self.execute_stage(self.ffmpeg_cmd_dual_stage2, "双流第2步：生成主视频无声60帧版",
                         lambda success, msg: self.on_dual_stage2_finished_new(success, msg, main_video_path, interference_video_path, output_path, temp_dir, target_width, target_height, aac_file, main_name, track1))

    def on_dual_stage2_finished_new(self, success, message, main_video_path, interference_video_path, output_path, temp_dir, target_width, target_height, aac_file, main_name, track1):
        """双流第2步完成回调（双飞AB算法）"""
        import os

        if not success:
            QMessageBox.warning(self, "警告", f"双流第2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 检查track1文件是否成功生成
        if not os.path.exists(track1):
            QMessageBox.warning(self, "警告", "双流第2步：未能生成主视频无声版")
            self.cleanup_and_continue(temp_dir)
            return

        # 第3步：生成主副交错帧视频（双飞AB算法）
        self.statusBar().showMessage(f"双流第3步 {self.processed_pairs+1}/{self.total_pairs}: 生成主副交错帧视频")

        track0 = os.path.join(temp_dir, f"{main_name}_track0.mp4")
        main_frames_dir = os.path.join(temp_dir, "main_frames")
        aux_frames_dir = os.path.join(temp_dir, "aux_frames")
        out_frames_dir = os.path.join(temp_dir, "out_frames")

        # 创建目录
        os.makedirs(main_frames_dir, exist_ok=True)
        os.makedirs(aux_frames_dir, exist_ok=True)
        os.makedirs(out_frames_dir, exist_ok=True)

        # 3.1 导出主视频30帧图片序列
        main_pattern = os.path.join(main_frames_dir, "main_%05d.jpg")
        cmd_main_frames = [
            '.\\ffmpeg\\ffmpeg.exe', '-y', '-i', main_video_path,
            '-vf', f'fps=30,scale={target_width}:{target_height},setsar=1/1',
            main_pattern
        ]

        # 执行主视频帧提取
        self.execute_stage(cmd_main_frames, "双流第3.1步：导出主视频帧",
                         lambda success, msg: self.on_dual_stage3_1_finished(success, msg, interference_video_path, output_path, temp_dir, target_width, target_height, aac_file, main_name, track0, track1, main_frames_dir, aux_frames_dir, out_frames_dir))

    def on_dual_stage3_1_finished(self, success, message, interference_video_path, output_path, temp_dir, target_width, target_height, aac_file, main_name, track0, track1, main_frames_dir, aux_frames_dir, out_frames_dir):
        """双流第3.1步完成回调"""
        import os

        if not success:
            QMessageBox.warning(self, "警告", f"双流第3.1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 3.2 导出副视频30帧图片序列
        aux_pattern = os.path.join(aux_frames_dir, "aux_%05d.jpg")
        cmd_aux_frames = [
            '.\\ffmpeg\\ffmpeg.exe', '-y', '-i', interference_video_path,
            '-vf', f'fps=30,scale={target_width}:{target_height},setsar=1/1',
            aux_pattern
        ]

        # 执行副视频帧提取
        self.execute_stage(cmd_aux_frames, "双流第3.2步：导出副视频帧",
                         lambda success, msg: self.on_dual_stage3_2_finished(success, msg, output_path, temp_dir, aac_file, main_name, track0, track1, main_frames_dir, aux_frames_dir, out_frames_dir))

    def on_dual_stage3_2_finished(self, success, message, output_path, temp_dir, aac_file, main_name, track0, track1, main_frames_dir, aux_frames_dir, out_frames_dir):
        """双流第3.2步完成回调"""
        import os
        import shutil

        if not success:
            QMessageBox.warning(self, "警告", f"双流第3.2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 获取帧文件列表
        main_imgs = sorted([f for f in os.listdir(main_frames_dir) if f.endswith('.jpg')])
        aux_imgs = sorted([f for f in os.listdir(aux_frames_dir) if f.endswith('.jpg')])

        if not main_imgs:
            QMessageBox.warning(self, "警告", "未找到主视频帧")
            self.cleanup_and_continue(temp_dir)
            return

        N = len(main_imgs)

        # 副帧不够循环补齐
        if len(aux_imgs) < N:
            aux_imgs = (aux_imgs * ((N // len(aux_imgs)) + 1))[:N]

        # 主副主副交错生成2N张图片（双飞AB算法）
        idx = 1
        for i in range(N):
            # 主
            src_main = os.path.join(main_frames_dir, main_imgs[i])
            dst_main = os.path.join(out_frames_dir, f"out_{idx:05d}.jpg")
            shutil.copyfile(src_main, dst_main)
            idx += 1
            # 副
            src_aux = os.path.join(aux_frames_dir, aux_imgs[i])
            dst_aux = os.path.join(out_frames_dir, f"out_{idx:05d}.jpg")
            shutil.copyfile(src_aux, dst_aux)
            idx += 1

        # 用FFmpeg合成60帧视频
        out_pattern = os.path.join(out_frames_dir, "out_%05d.jpg")
        cmd_img2vid = [
            '.\\ffmpeg\\ffmpeg.exe', '-y', '-framerate', '60', '-start_number', '1',
            '-i', out_pattern, '-c:v', 'libx264', '-pix_fmt', 'yuv420p',
            track0
        ]

        # 执行图片合成视频
        self.execute_stage(cmd_img2vid, "双流第3.3步：合成交错视频",
                         lambda success, msg: self.on_dual_stage3_3_finished(success, msg, output_path, temp_dir, aac_file, main_name, track0, track1))

    def on_dual_stage3_3_finished(self, success, message, output_path, temp_dir, aac_file, main_name, track0, track1):
        """双流第3.3步完成回调"""
        import os
        from datetime import datetime

        if not success:
            QMessageBox.warning(self, "警告", f"双流第3.3步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第4步：最终多轨合成（双流AB算法）
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        final_output = os.path.join(os.path.dirname(output_path), f"{main_name}_双流AB_{timestamp}.mp4")

        cmd_final = [
            '.\\ffmpeg\\ffmpeg.exe', '-y', '-i', track0, '-i', track1, '-i', aac_file,
            '-map', '0:v', '-map', '1:v', '-map', '2:a', '-c', 'copy',
            '-r', '60',
            '-disposition:v:0', 'forced', '-disposition:v:1', 'default',
            '-metadata:s:v:0', 'title=交错画面', '-metadata:s:v:1', 'title=原始画面',
            final_output
        ]

        self.statusBar().showMessage(f"双流第4步 {self.processed_pairs+1}/{self.total_pairs}: 最终多轨合成")
        self.execute_stage(cmd_final, "双流第4步：最终多轨合成",
                         lambda success, msg: self.on_dual_final_finished(success, msg, final_output, temp_dir))

    def on_dual_final_finished(self, success, message, final_output, temp_dir):
        """双流最终完成回调（双飞AB算法）"""
        import os

        if success and os.path.exists(final_output):
            file_size = os.path.getsize(final_output)
            print(f"✅ 双流模式处理完成")
            print(f"   输出文件: {final_output}")
            print(f"   文件大小: {file_size / (1024*1024):.1f} MB")
            print(f"   特性: 多轨道输出，交错画面(forced) + 原始画面(default)")
            self.statusBar().showMessage(f"双流模式完成处理 {self.processed_pairs+1}/{self.total_pairs}")
        else:
            print(f"❌ 双流模式处理失败: {message}")
            QMessageBox.warning(self, "警告", f"双流模式处理失败: {message}")

        # 清理临时文件并继续下一个
        self.cleanup_and_continue(temp_dir)

















    def process_jidu213_mode(self, main_video, aux_video, output_file):
        """季度213模式处理 - 使用笔墨AB的红薯效果处理流程"""
        import tempfile
        import shutil
        import os
        import subprocess
        from datetime import datetime

        # 获取主视频时长和尺寸信息
        duration = self.get_video_duration(main_video)
        if duration is None:
            QMessageBox.critical(self, "错误", f"无法获取主视频时长: {main_video}")
            self.process_next_pair()
            return

        # 获取主视频尺寸信息
        try:
            width_a, height_a = self.get_video_dimensions(main_video)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"获取主视频尺寸失败，使用默认值: {e}")
            width_a, height_a = 1080, 1920

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="jidu213_")

        try:
            self.statusBar().showMessage(f"季度213模式处理 {self.processed_pairs+1}/{self.total_pairs} - 红薯效果处理")

            # 检查输入文件是否存在
            if not os.path.exists(aux_video):
                QMessageBox.critical(self, "错误", f"辅助视频文件不存在: {aux_video}")
                self.cleanup_and_continue(temp_dir)
                return

            # 将时长转换为字符串供命令使用
            duration_str = str(duration)

            # 定义临时文件路径
            temp_B = os.path.join(temp_dir, "temp_B.mp4")
            temp_A = os.path.join(temp_dir, "temp_A.mp4")
            temp_C = os.path.join(temp_dir, "temp_C.mp4")
            temp_D = os.path.join(temp_dir, "temp_D.mp4")
            temp_E = os.path.join(temp_dir, "temp_E.mkv")
            temp_F = os.path.join(temp_dir, "temp_F.mkv")

            # 翻天AB算法 - 第1步：视频去重处理（处理主视频B）
            cmd1 = [
                ".\\ffmpeg\\ffmpeg.exe", "-hwaccel", "none", "-y", "-i", main_video,
                "-vf", f"scale={width_a}:{height_a},setsar=1:1,fps=60",
                "-c:v", "libx264", "-preset", "4", "-b:v", "15000k", "-maxrate:v", "15000k", "-bufsize:v", "15000k",
                temp_A
            ]

            self.statusBar().showMessage(f"季度213第1步：视频去重处理 {self.processed_pairs+1}/{self.total_pairs}")
            self.execute_stage(cmd1, "季度213第1步：视频去重处理",
                             lambda success, msg: self.on_jidu213_stage1_finished(success, msg, main_video, aux_video, output_file, temp_dir, duration_str, width_a, height_a, temp_A, temp_B, temp_C, temp_D, temp_E, temp_F))

        except Exception as e:
            QMessageBox.critical(self, "错误", f"季度213模式处理失败: {str(e)}")
            self.cleanup_and_continue(temp_dir)

    def on_jidu213_stage1_finished(self, success, message, main_video, aux_video, output_file, temp_dir, duration_str, width_a, height_a, temp_A, temp_B, temp_C, temp_D, temp_E, temp_F):
        """季度213第1步完成回调"""
        import os
        if not success or not os.path.exists(temp_A):
            QMessageBox.critical(self, "错误", f"季度213第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第2步：视频二次处理（处理副视频A）
        cmd2 = [
            ".\\ffmpeg\\ffmpeg.exe", "-hwaccel", "none", "-y", "-stream_loop", "-1", "-i", aux_video, "-t", duration_str,
            "-vf", f"scale={width_a}:{height_a},setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "4", "-b:v", "15000k", "-maxrate:v", "15000k", "-bufsize:v", "15000k", "-an",
            temp_B
        ]

        self.statusBar().showMessage(f"季度213第2步：视频二次处理 {self.processed_pairs+1}/{self.total_pairs}")
        self.execute_stage(cmd2, "季度213第2步：视频二次处理",
                         lambda success, msg: self.on_jidu213_stage2_finished(success, msg, main_video, aux_video, output_file, temp_dir, duration_str, width_a, height_a, temp_A, temp_B, temp_C, temp_D, temp_E, temp_F))

    def on_jidu213_stage2_finished(self, success, message, main_video, aux_video, output_file, temp_dir, duration_str, width_a, height_a, temp_A, temp_B, temp_C, temp_D, temp_E, temp_F):
        """季度213第2步完成回调"""
        import os
        if not success or not os.path.exists(temp_B):
            QMessageBox.critical(self, "错误", f"季度213第2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第3步：独家算法融合中（翻天AB的核心合并算法）
        cmd3 = [
            ".\\ffmpeg\\ffmpeg.exe", "-hwaccel", "none", "-y", "-i", temp_A, "-i", temp_B,
            "-filter_complex", "[0:v]setsar=1/1,select='not(mod(n,2))'[a];[1:v]setsar=1/1,select='mod(n,2)'[b];[a][b]interleave,select='not(eq(n,0))'[v]",
            "-map", "[v]", "-c:v", "libx264", "-vsync", "vfr", "-b:v", "15000k", "-maxrate", "15000k", "-minrate", "15000k", "-bufsize", "15000k",
            "-rc", "cbr", "-bf", "0", "-video_track_timescale", "1000000", "-map", "0:a?", "-c:a", "copy", "-t", duration_str,
            output_file
        ]

        self.statusBar().showMessage(f"季度213第3步：独家算法融合中 {self.processed_pairs+1}/{self.total_pairs}")
        self.execute_stage(cmd3, "季度213第3步：独家算法融合中",
                         lambda success, msg: self.on_jidu213_final_finished(success, msg, output_file, temp_dir))



    def on_jidu213_final_finished(self, success, message, output_file, temp_dir):
        """季度213最终步骤完成回调"""
        import os

        if success:
            self.statusBar().showMessage(f"季度213模式完成处理 {self.processed_pairs+1}/{self.total_pairs}")
            print(f"✅ 季度2B模式处理完成")
            print(f"   输出文件: {output_file}")
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   文件大小: {file_size / (1024*1024):.1f} MB")
        else:
            QMessageBox.warning(self, "警告", f"季度213最终步骤失败: {message}")

        # 清理临时文件并继续下一对
        self.cleanup_and_continue(temp_dir)

    def process_jidusb_mode(self, main_video, aux_video, output_file):
        """季度SB模式处理 - 使用无极AB的多轨合成算法"""
        # 获取主视频时长
        duration = self.get_video_duration(main_video)
        if duration is None:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 获取主视频分辨率
        resolution = self.get_video_resolution(main_video)
        if not resolution:
            self.processed_pairs += 1
            self.process_next_pair()
            return

        # 解析主视频分辨率
        try:
            width, height = resolution.split('x')
            main_width, main_height = int(width), int(height)
        except:
            QMessageBox.warning(self, "警告", f"无法解析主视频分辨率: {resolution}")
            self.processed_pairs += 1
            self.process_next_pair()
            return

        import tempfile
        import os
        temp_dir = tempfile.mkdtemp()

        # 创建临时文件路径
        temp_b_silent = os.path.join(temp_dir, "b_silent_scaled.mp4")  # 副视频处理结果
        duration_str = f"{duration:.2f}"

        self.statusBar().showMessage(f"季度SB模式处理 {self.processed_pairs+1}/{self.total_pairs} - 大羊鞭AB算法")

        # 第1步：副视频静音+缩放+补时长（无极AB算法）
        self.ffmpeg_cmd_jidusb_step1 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-stream_loop", "-1", "-i", aux_video, "-t", duration_str,
            "-vf", f"scale={main_width}:{main_height}",
            "-an", "-c:v", "libx264", "-preset", "medium",
            temp_b_silent
        ]

        # 执行第一步处理
        self.execute_stage(self.ffmpeg_cmd_jidusb_step1, "季度SB第1步：副视频静音+缩放+补时长",
                         lambda success, msg: self.on_jidusb_step1_finished(success, msg, main_video, duration_str, temp_b_silent, output_file, temp_dir))

    def on_jidusb_step1_finished(self, success, message, main_video, duration_str, temp_b_silent, output_file, temp_dir):
        """季度SB第1步完成回调"""
        import os
        if not success or not os.path.exists(temp_b_silent):
            QMessageBox.warning(self, "警告", f"季度SB第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 第2步：多轨合成（无极AB算法）
        self.ffmpeg_cmd_jidusb_step2 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-hide_banner", "-i", temp_b_silent, "-i", main_video,
            "-map", "0:v:0", "-map", "1:v:0", "-map", "1:a:0",
            "-c:v", "libx264", "-crf", "18", "-preset", "medium", "-c:a", "aac", "-b:a", "320k",
            "-disposition:v:0", "0", "-disposition:v:1", "default", "-disposition:a:0", "default",
            "-map_metadata", "-1",
            output_file
        ]

        self.statusBar().showMessage(f"季度SB第2步：多轨合成 {self.processed_pairs+1}/{self.total_pairs}")
        self.execute_stage(self.ffmpeg_cmd_jidusb_step2, "季度SB第2步：多轨合成",
                         lambda success, msg: self.on_jidusb_final_finished(success, msg, output_file, temp_dir))

    def on_jidusb_final_finished(self, success, message, output_file, temp_dir):
        """季度SB最终完成回调"""
        import os

        if success:
            self.statusBar().showMessage(f"季度SB模式完成处理 {self.processed_pairs+1}/{self.total_pairs}")
            print(f"✅ 季度SB模式处理完成")
            print(f"   输出文件: {output_file}")
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   文件大小: {file_size / (1024*1024):.1f} MB")
                print(f"   特性: 多轨道输出，播放器可切换轨道")
        else:
            QMessageBox.warning(self, "警告", f"季度SB最终步骤失败: {message}")

        # 清理临时文件并继续下一对
        self.cleanup_and_continue(temp_dir)

    def process_dayangbian_mode(self, main_video, aux_video, output_file):
        """大羊鞭模式处理 - 使用陀螺AB.py的FFmpeg命令（7步处理）"""
        # 获取主视频时长
        duration = self.get_video_duration(main_video)
        if duration is None:
            QMessageBox.critical(self, "错误", f"无法获取主视频时长: {main_video}")
            self.process_next_pair()
            return

        import tempfile
        import os
        import subprocess
        temp_dir = tempfile.mkdtemp(prefix="dayangbian_")

        # 创建临时文件路径（陀螺AB的7步处理）
        a1_file = os.path.join(temp_dir, "a1.mp4")
        a0_file = os.path.join(temp_dir, "a0.mp4")
        b1_file = os.path.join(temp_dir, "b1.mp4")
        a0b1_file = os.path.join(temp_dir, "a0b1.mp4")
        a1b1_file = os.path.join(temp_dir, "a1b1.mp4")
        a1b1c1_file = os.path.join(temp_dir, "a1b1c1.mp4")

        # 大羊鞭模式始终使用CPU编码（libx264）

        # 大羊鞭第1步：处理主视频（陀螺AB命令）- 始终使用CPU编码
        self.ffmpeg_cmd_dayangbian_stage1 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", main_video,
            "-vf", "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
            "-b:v", "15000k", "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
            "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain", "-c:a", "copy", "-threads", "0",
            "-t", str(duration), a1_file
        ]

        self.statusBar().showMessage(f"大羊鞭第1步 {self.processed_pairs+1}/{self.total_pairs}: 处理主视频")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage1, "大羊鞭第1步：处理主视频",
                         lambda success, msg: self.on_dayangbian_stage1_finished(success, msg, aux_video, output_file, temp_dir, a1_file, a0_file, duration))

    def on_dayangbian_stage1_finished(self, success, message, aux_video, output_file, temp_dir, a1_file, a0_file, duration):
        """大羊鞭第1步完成回调"""
        import os

        if not success or not os.path.exists(a1_file):
            QMessageBox.critical(self, "错误", f"大羊鞭第1步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 大羊鞭第2步：提取片段（陀螺AB命令）- 始终使用CPU编码
        self.ffmpeg_cmd_dayangbian_stage2 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", a1_file, "-ss", "00:00:00", "-t", "0.333",
            "-vf", "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
            "-b:v", "15000k", "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
            "-movflags", "+faststart", "-flags", "+cgop", "-init_qpP", "0",
            "-quality", "best", "-rc", "cbr", "-threads", "0", a0_file
        ]

        self.statusBar().showMessage(f"大羊鞭第2步 {self.processed_pairs+1}/{self.total_pairs}: 提取片段")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage2, "大羊鞭第2步：提取片段",
                         lambda success, msg: self.on_dayangbian_stage2_finished(success, msg, aux_video, output_file, temp_dir, a1_file, a0_file, duration))

    def on_dayangbian_stage2_finished(self, success, message, aux_video, output_file, temp_dir, a1_file, a0_file, duration):
        """大羊鞭第2步完成回调"""
        import os

        if not success or not os.path.exists(a0_file):
            QMessageBox.critical(self, "错误", f"大羊鞭第2步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 重新定义文件路径
        b1_file = os.path.join(temp_dir, "b1.mp4")
        a0b1_file = os.path.join(temp_dir, "a0b1.mp4")

        # 大羊鞭第3步：处理背景视频（陀螺AB命令）- 始终使用CPU编码
        self.ffmpeg_cmd_dayangbian_stage3 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-stream_loop", "-1", "-i", aux_video,
            "-vf", "scale=1080:1920:force_original_aspect_ratio=increase,crop=1080:1920:(iw-ow)/2:(ih-oh)/2,setsar=1:1,fps=60",
            "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
            "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
            "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain", "-an", "-threads", "0",
            "-t", str(duration), b1_file
        ]

        self.statusBar().showMessage(f"大羊鞭第3步 {self.processed_pairs+1}/{self.total_pairs}: 处理背景视频")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage3, "大羊鞭第3步：处理背景视频",
                         lambda success, msg: self.on_dayangbian_stage3_finished(success, msg, output_file, temp_dir, a1_file, a0_file, b1_file, a0b1_file, duration))

    def on_dayangbian_stage3_finished(self, success, message, output_file, temp_dir, a1_file, a0_file, b1_file, a0b1_file, duration):
        """大羊鞭第3步完成回调"""
        import os

        if not success or not os.path.exists(b1_file):
            QMessageBox.critical(self, "错误", f"大羊鞭第3步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 大羊鞭第4步：视频拼接（陀螺AB命令）- 始终使用CPU编码
        self.ffmpeg_cmd_dayangbian_stage4 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", a0_file, "-i", b1_file,
            "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={duration}",
            "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
            "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
            "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
            "-deblock", "1:1", "-tune", "grain", "-threads", "0",
            "-t", str(duration), a0b1_file
        ]

        self.statusBar().showMessage(f"大羊鞭第4步 {self.processed_pairs+1}/{self.total_pairs}: 视频拼接")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage4, "大羊鞭第4步：视频拼接",
                         lambda success, msg: self.on_dayangbian_stage4_finished(success, msg, output_file, temp_dir, a1_file, a0b1_file, duration))

    def on_dayangbian_stage4_finished(self, success, message, output_file, temp_dir, a1_file, a0b1_file, duration):
        """大羊鞭第4步完成回调"""
        import os

        if not success or not os.path.exists(a0b1_file):
            QMessageBox.critical(self, "错误", f"大羊鞭第4步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 重新定义文件路径
        a1b1_file = os.path.join(temp_dir, "a1b1.mp4")

        # 大羊鞭第5步：闪烁效果叠加（陀螺AB命令）- 始终使用CPU编码
        self.ffmpeg_cmd_dayangbian_stage5 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", a1_file, "-i", a0b1_file,
            "-filter_complex", "[0:v]setpts=PTS-STARTPTS,fps=60[base];[1:v]setpts=PTS-STARTPTS,format=yuv420p,format=rgba,colorchannelmixer=aa=0.5[flash];[base][flash]overlay=enable='if(eq(mod(n,2),1),1,0)'[outv]",
            "-map", "[outv]", "-map", "0:a", "-c:v", "libx264", "-preset", "faster",
            "-crf", "23", "-c:a", "copy", "-x264-params", "level=6.2:ref=4",
            "-threads", "0", "-t", str(duration), "-video_track_timescale", "1000k", a1b1_file
        ]

        self.statusBar().showMessage(f"大羊鞭第5步 {self.processed_pairs+1}/{self.total_pairs}: 闪烁效果叠加")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage5, "大羊鞭第5步：闪烁效果叠加",
                         lambda success, msg: self.on_dayangbian_stage5_finished(success, msg, output_file, temp_dir, a1b1_file, a0b1_file, duration))

    def on_dayangbian_stage5_finished(self, success, message, output_file, temp_dir, a1b1_file, a0b1_file, duration):
        """大羊鞭第5步完成回调"""
        import os

        if not success or not os.path.exists(a1b1_file):
            QMessageBox.critical(self, "错误", f"大羊鞭第5步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 重新定义文件路径
        a1b1c1_file = os.path.join(temp_dir, "a1b1c1.mp4")

        # 大羊鞭第6步：时间轴调整（陀螺AB命令）- 始终使用CPU编码
        self.ffmpeg_cmd_dayangbian_stage6 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", a1b1_file,
            "-vf", "[0:v]setpts='N/FRAME_RATE/TB-666.5',select='not(eq(n,0))'",
            "-vsync", "0", "-global_quality", "23", "-c:v", "libx264",
            "-preset", "faster", "-movflags", "faststart", "-t", str(duration),
            "-video_track_timescale", "1000k", a1b1c1_file
        ]

        self.statusBar().showMessage(f"大羊鞭第6步 {self.processed_pairs+1}/{self.total_pairs}: 时间轴调整")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage6, "大羊鞭第6步：时间轴调整",
                         lambda success, msg: self.on_dayangbian_stage6_finished(success, msg, output_file, temp_dir, a0b1_file, a1b1c1_file, duration))

    def on_dayangbian_stage6_finished(self, success, message, output_file, temp_dir, a0b1_file, a1b1c1_file, duration):
        """大羊鞭第6步完成回调"""
        import os

        if not success or not os.path.exists(a1b1c1_file):
            QMessageBox.critical(self, "错误", f"大羊鞭第6步失败: {message}")
            self.cleanup_and_continue(temp_dir)
            return

        # 大羊鞭第7步：最终合成（陀螺AB命令）
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        final_output = os.path.join(os.path.dirname(output_file), f"{timestamp}_大羊鞭AB_{os.path.splitext(os.path.basename(output_file))[0]}.mp4")

        self.ffmpeg_cmd_dayangbian_stage7 = [
            ".\\ffmpeg\\ffmpeg.exe", "-y", "-i", a0b1_file, "-i", a1b1c1_file,
            "-map", "0:v:0", "-map", "1:v:0", "-map", "1:a:0", "-c", "copy",
            "-movflags", "faststart", "-disposition:v:0", "0",
            "-disposition:v:1", "default", "-disposition:a:1", "default",
            "-t", str(duration), final_output
        ]

        self.statusBar().showMessage(f"大羊鞭第7步 {self.processed_pairs+1}/{self.total_pairs}: 最终合成")
        self.execute_stage(self.ffmpeg_cmd_dayangbian_stage7, "大羊鞭第7步：最终合成",
                         lambda success, msg: self.on_dayangbian_final_finished(success, msg, final_output, temp_dir))

    def on_dayangbian_final_finished(self, success, message, output_file, temp_dir):
        """大羊鞭最终完成回调"""
        import os

        if success:
            self.statusBar().showMessage(f"大羊鞭模式完成处理 {self.processed_pairs+1}/{self.total_pairs}")
            print(f"✅ 大羊鞭模式处理完成")
            print(f"   🎯 应用技术: 大羊鞭算法 (陀螺AB 7步处理流程)")
            print(f"   📊 处理步骤: 主视频处理 → 片段提取 → 背景处理 → 视频拼接 → 闪烁叠加 → 时间轴调整 → 最终合成")
            print(f"   🔄 输出格式: 多轨道视频 (双视频轨道)")
            print(f"   🎨 视频效果: 1080x1920竖屏 + 60fps + 闪烁效果")
            print(f"   💻 编码方式: libx264 (15000k码率, high profile)")
            print(f"   📁 输出文件: {output_file}")
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   📦 文件大小: {file_size / (1024*1024):.1f} MB")
        else:
            QMessageBox.critical(self, "错误", f"大羊鞭最终步骤失败: {message}")

        # 清理临时文件并继续下一对
        self.cleanup_and_continue(temp_dir)



if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = VideoProcessorGUI()
    window.show()
    sys.exit(app.exec_())
