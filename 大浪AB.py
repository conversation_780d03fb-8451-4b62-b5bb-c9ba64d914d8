import os
import sys
import threading
import tempfile
import subprocess
from datetime import datetime

def get_video_duration(video_path, ffprobe_path):
    """获取视频时长"""
    cmd = [ffprobe_path, '-v', 'quiet', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path]
    result = subprocess.run(cmd, capture_output=True, text=True)
    try:
        return float(result.stdout.strip())
    except Exception:
        return 0.0

class 大浪AB:
    def __init__(self, log_callback=None, ffmpeg_path=None, ffprobe_path=None):
        self.log_callback = log_callback
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path

    def log(self, message):
        if self.log_callback:
            self.log_callback(message)

    def run_cmd(self, cmd):
        """执行FFmpeg命令"""
        # 在Windows上隐藏控制台窗口
        startupinfo = None
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
        
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore',
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{' '.join(cmd)}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")

    def process_single_video(self, main_file, background_file, output_dir, del_a=False, del_b=False):
        """处理单个视频文件"""
        try:
            # 获取主视频时长并计算输出时长
            input_duration = get_video_duration(main_file, self.ffprobe_path)
            if input_duration <= 0:
                raise RuntimeError("无法获取主视频时长")
            
            # 按照新命令的规律：使用输入视频的实际时长，并添加微调
            main_duration = input_duration + 0.003991
            
            basename = os.path.splitext(os.path.basename(main_file))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            self.log(f"开始处理：{basename}")
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # 步骤1: 处理主视频
                self.log(f"⚡ 第一步：主视频处理中...")
                a1_file = os.path.join(temp_dir, "a1.mp4")
                cmd1 = [
                    self.ffmpeg_path, "-y", "-i", main_file,
                    "-vf", "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                    "-b:v", "15000k", "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                    "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                    "-deblock", "1:1", "-tune", "grain", "-c:a", "copy", "-threads", "0",
                    "-t", str(main_duration), a1_file
                ]
                self.run_cmd(cmd1)

                # 步骤2: 提取片段
                self.log(f"🎬 第二步：片段提取中...")
                a0_file = os.path.join(temp_dir, "a0.mp4")
                cmd2 = [
                    self.ffmpeg_path, "-y", "-i", a1_file, "-ss", "00:00:00", "-t", "0.333",
                    "-vf", "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2,setsar=1:1,fps=60",
                    "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                    "-b:v", "15000k", "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                    "-movflags", "+faststart", "-flags", "+cgop", "-init_qpP", "0",
                    "-quality", "best", "-rc", "cbr", "-threads", "0", a0_file
                ]
                self.run_cmd(cmd2)

                # 步骤3: 处理背景视频
                self.log(f"🎭 第三步：背景视频处理中...")
                b1_file = os.path.join(temp_dir, "b1.mp4")
                cmd3 = [
                    self.ffmpeg_path, "-y", "-stream_loop", "-1", "-i", background_file,
                    "-vf", "scale=1080:1920:force_original_aspect_ratio=increase,crop=1080:1920:(iw-ow)/2:(ih-oh)/2,setsar=1:1,fps=60",
                    "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                    "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                    "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                    "-deblock", "1:1", "-tune", "grain", "-an", "-threads", "0",
                    "-t", str(main_duration), b1_file
                ]
                self.run_cmd(cmd3)

                # 步骤4: 视频拼接
                self.log(f"🔗 第四步：去重处理中...")
                a0b1_file = os.path.join(temp_dir, "a0b1.mp4")
                cmd4 = [
                    self.ffmpeg_path, "-y", "-i", a0_file, "-i", b1_file,
                    "-filter_complex", f"[0:v][1:v]concat=n=2:v=1:a=0,trim=duration={main_duration}",
                    "-c:v", "libx264", "-preset", "faster", "-x264-params", "nal-hrd=cbr",
                    "-profile:v", "high", "-level", "4.0", "-pix_fmt", "yuv420p",
                    "-movflags", "+faststart", "-flags", "+cgop", "-aq-strength", "0.8",
                    "-deblock", "1:1", "-tune", "grain", "-threads", "0",
                    "-t", str(main_duration), a0b1_file
                ]
                self.run_cmd(cmd4)

                # 步骤5: 闪烁效果叠加
                self.log(f"✨ 第五步：过审处理中...")
                a1b1_file = os.path.join(temp_dir, "a1b1.mp4")
                cmd5 = [
                    self.ffmpeg_path, "-y", "-i", a1_file, "-i", a0b1_file,
                    "-filter_complex", "[0:v]setpts=PTS-STARTPTS,fps=60[base];[1:v]setpts=PTS-STARTPTS,format=yuv420p,format=rgba,colorchannelmixer=aa=0.5[flash];[base][flash]overlay=enable='if(eq(mod(n,2),1),1,0)'[outv]",
                    "-map", "[outv]", "-map", "0:a", "-c:v", "libx264", "-preset", "faster",
                    "-crf", "23", "-c:a", "copy", "-x264-params", "level=6.2:ref=4",
                    "-threads", "0", "-t", str(main_duration), "-video_track_timescale", "1000k", a1b1_file
                ]
                self.run_cmd(cmd5)

                # 步骤6: 时间轴调整
                self.log(f"⏰ 第六步：爆量处理中...")
                a1b1c1_file = os.path.join(temp_dir, "a1b1c1.mp4")
                cmd6 = [
                    self.ffmpeg_path, "-y", "-i", a1b1_file,
                    "-vf", "[0:v]setpts='N/FRAME_RATE/TB-666.5',select='not(eq(n,0))'",
                    "-vsync", "0", "-global_quality", "23", "-c:v", "libx264",
                    "-preset", "faster", "-movflags", "faststart", "-t", str(main_duration),
                    "-video_track_timescale", "1000k", a1b1c1_file
                ]
                self.run_cmd(cmd6)

                # 步骤7: 最终合成
                self.log(f"🎯 第七步：最终合成中...")
                output_file = os.path.join(output_dir, f"{timestamp}-{basename}大浪AB_{os.path.basename(main_file)}")
                cmd7 = [
                    self.ffmpeg_path, "-y", "-i", a0b1_file, "-i", a1b1c1_file,
                    "-map", "0:v:0", "-map", "1:v:0", "-map", "1:a:0", "-c", "copy",
                    "-movflags", "faststart", "-disposition:v:0", "0",
                    "-disposition:v:1", "default", "-disposition:a:1", "default",
                    "-t", str(main_duration), output_file
                ]
                self.run_cmd(cmd7)
                
                self.log(f"处理完成：{output_file}")

                # 删除源文件（如果启用）
                if del_a and os.path.exists(main_file):
                    try:
                        os.remove(main_file)
                        self.log(f"已删除源文件：{main_file}")
                    except Exception as e:
                        self.log(f"删除源文件失败：{e}")
                
                if del_b and os.path.exists(background_file):
                    try:
                        os.remove(background_file)
                        self.log(f"已删除背景文件：{background_file}")
                    except Exception as e:
                        self.log(f"删除背景文件失败：{e}")
                
        except Exception as e:
            self.log(f"处理失败：{e}")
            raise

    def run_processing_logic(self, video_a_list, video_b_list, output_dir_path, is_batch, del_a, del_b):
        """主要的处理逻辑入口"""
        try:
            if is_batch:
                # 批量模式
                if not video_a_list or not video_b_list:
                    self.log("错误：批量模式下需要提供主视频文件夹和背景视频文件夹")
                    return
                
                # 获取文件夹中的所有视频文件
                video_extensions = ('.mp4', '.mkv', '.mov', '.avi')
                main_files = [f for f in os.listdir(video_a_list[0]) if f.lower().endswith(video_extensions)]
                background_files = [f for f in os.listdir(video_b_list[0]) if f.lower().endswith(video_extensions)]
                
                main_files.sort()
                background_files.sort()
                
                if not main_files:
                    self.log("错误：主视频文件夹中没有找到视频文件！")
                    return
                
                if not background_files:
                    self.log("错误：背景视频文件夹中没有找到视频文件！")
                    return
                
                self.log(f"找到 {len(main_files)} 个主视频文件，{len(background_files)} 个背景视频文件，开始批量处理...")
                
                # 确保一对一匹配
                for idx, main_file in enumerate(main_files):
                    if idx >= len(background_files):
                        self.log(f"警告：背景视频数量不足，跳过剩余的主视频")
                        break
                    
                    main_path = os.path.join(video_a_list[0], main_file)
                    background_path = os.path.join(video_b_list[0], background_files[idx])
                    
                    basename = os.path.splitext(main_file)[0]
                    self.log(f"[{idx+1}/{len(main_files)}] 处理文件：{main_file}")
                    
                    try:
                        self.process_single_video(main_path, background_path, output_dir_path, del_a, del_b)
                    except Exception as e:
                        self.log(f"❌ 失败：{main_file}，原因：{e}")
                        continue
                
                self.log(f"大浪AB批量处理完成")
                
            else:
                # 单文件模式
                if not video_a_list or not video_b_list:
                    self.log("错误：请选择主视频和背景视频")
                    return
                
                main_file = video_a_list[0]
                background_file = video_b_list[0]
                
                self.log("大浪AB处理中请等待")
                self.process_single_video(main_file, background_file, output_dir_path, del_a, del_b)
                self.log("大浪AB处理完成")
                
        except Exception as e:
            self.log(f"大浪AB处理失败：{e}")
            raise 