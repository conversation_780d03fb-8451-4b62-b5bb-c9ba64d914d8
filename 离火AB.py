import os
import sys
import threading
import tempfile
import subprocess
from datetime import datetime

def get_ffmpeg_path():
    # 获取程序所在目录
    if getattr(sys, 'frozen', False):
        # 打包后的路径
        exe_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境路径
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    
    # 尝试多个可能的路径
    possible_paths = [
        os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffmpeg.exe'),
        os.path.join(exe_dir, 'ffmpeg', 'ffmpeg.exe'),
        os.path.join(exe_dir, 'ffmpeg.exe'),
        os.path.join(exe_dir, 'bin', 'ffmpeg.exe'),
        os.path.join(exe_dir, 'tools', 'ffmpeg.exe')
    ]
    
    for ffmpeg_path in possible_paths:
        if os.path.exists(ffmpeg_path):
            return ffmpeg_path
    
    raise FileNotFoundError(f"未找到ffmpeg，请确保ffmpeg.exe在程序目录中")

def get_ffprobe_path():
    # 获取程序所在目录
    if getattr(sys, 'frozen', False):
        # 打包后的路径
        exe_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境路径
        exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    
    # 尝试多个可能的路径
    possible_paths = [
        os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffprobe.exe'),
        os.path.join(exe_dir, 'ffmpeg', 'ffprobe.exe'),
        os.path.join(exe_dir, 'ffprobe.exe'),
        os.path.join(exe_dir, 'bin', 'ffprobe.exe'),
        os.path.join(exe_dir, 'tools', 'ffprobe.exe')
    ]
    
    for ffprobe_path in possible_paths:
        if os.path.exists(ffprobe_path):
            return ffprobe_path
    
    raise FileNotFoundError(f"未找到ffprobe，请确保ffprobe.exe在程序目录中")

def get_video_duration(video_path, ffprobe_path):
    """获取视频时长"""
    cmd = [ffprobe_path, '-v', 'quiet', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path]
    result = subprocess.run(cmd, capture_output=True, text=True)
    try:
        return float(result.stdout.strip())
    except Exception:
        return 0.0

def get_video_dimensions(video_path, ffprobe_path):
    """获取视频尺寸"""
    cmd = [ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'csv=p=0', video_path]
    result = subprocess.run(cmd, capture_output=True, text=True)
    try:
        width, height = map(int, result.stdout.strip().split(','))
        return width, height
    except Exception:
        return 1080, 2106  # 默认尺寸

class 离火AB:
    def __init__(self, log_callback=None, ffmpeg_path=None, ffprobe_path=None):
        self.log_callback = log_callback
        self.ffmpeg_path = ffmpeg_path
        self.ffprobe_path = ffprobe_path

    def log(self, message):
        if self.log_callback:
            self.log_callback(message)

    def run_cmd(self, cmd):
        """执行FFmpeg命令"""
        # 在Windows上隐藏控制台窗口
        startupinfo = None
        if os.name == 'nt':  # Windows系统
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
        
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore',
            startupinfo=startupinfo,
            creationflags=subprocess.CREATE_NO_WINDOW
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{' '.join(cmd)}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")

    def process_single_video(self, main_file, background_file, output_dir, del_a=False, del_b=False):
        """处理单个视频文件"""
        try:
            # 获取主视频时长并计算输出时长
            input_duration = get_video_duration(main_file, self.ffprobe_path)
            if input_duration <= 0:
                raise RuntimeError("无法获取主视频时长")
            
            # 获取主视频尺寸
            width, height = get_video_dimensions(main_file, self.ffprobe_path)
            
            # 按照新命令的规律：使用输入视频的实际时长，并添加微调
            main_duration = input_duration + 0.003991
            
            basename = os.path.splitext(os.path.basename(main_file))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            self.log(f"开始处理：{basename}")
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # 步骤1: 处理主视频
                self.log(f"🔥 第一步：主视频处理中...")
                a1_file = os.path.join(temp_dir, "a1.mp4")
                cmd1 = [
                    self.ffmpeg_path, "-y", "-i", main_file,
                    "-vf", f"scale={width}:{height},pad={width}:{height}:(ow-iw)/2:(oh-ih)/2,setsar=1,fps=30",
                    "-c:v", "libx264", "-preset", "faster", "-crf", "18",
                    "-x264-params", "nal-hrd=cbr", "-c:a", "copy",
                    "-t", str(main_duration), a1_file
                ]
                self.run_cmd(cmd1)

                # 步骤2: 处理背景视频
                self.log(f"🔥 第二步：背景视频处理中...")
                b1_file = os.path.join(temp_dir, "b1.mp4")
                cmd2 = [
                    self.ffmpeg_path, "-y", "-i", background_file,
                    "-vf", f"scale={width}:{height},setsar=1,fps=60",
                    "-c:v", "libx264", "-preset", "faster", "-crf", "18",
                    "-x264-params", "nal-hrd=cbr", "-c:a", "copy",
                    "-t", str(main_duration), b1_file
                ]
                self.run_cmd(cmd2)

                # 步骤3: 第一次合成
                self.log(f"🔥 第三步：第一次合成中...")
                a1_b1_file = os.path.join(temp_dir, "a1_b1.mkv")
                cmd3 = [
                    self.ffmpeg_path, "-y", "-i", a1_file, "-i", b1_file,
                    "-filter_complex", f"[0:v]scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1,trim=duration={main_duration}[v]",
                    "-map", "[v]", "-c:v", "libx264", "-preset", "faster",
                    "-b:v", "10000k", "-maxrate", "12000k", "-bufsize", "15000k",
                    "-threads", "0", "-x264-params", "level=6.2:ref=4", a1_b1_file
                ]
                self.run_cmd(cmd3)

                # 步骤4: 交错合成
                self.log(f"🔥 第四步：交错合成中...")
                a1b1_file = os.path.join(temp_dir, "a1b1.mkv")
                cmd4 = [
                    self.ffmpeg_path, "-y", "-i", a1_file, "-i", b1_file,
                    "-filter_complex", f"[1:v][0:v]scale2ref[v1s][v0r];[v0r]setsar=1,fps=30[v0];[v1s]scale={width}:{height}:force_original_aspect_ratio=disable,setsar=1,fps=30[v1];[v0][v1]interleave,trim=duration={main_duration}[v]",
                    "-map", "[v]", "-c:v", "libx264", "-preset", "faster", "-crf", "23",
                    "-x264-params", "level=6.2:ref=4", a1b1_file
                ]
                self.run_cmd(cmd4)

                # 步骤5: 最终合成
                self.log(f"🔥 第五步：最终合成中...")
                output_file = os.path.join(output_dir, f"{timestamp}-{basename}离火AB_{os.path.basename(main_file)}")
                cmd5 = [
                    self.ffmpeg_path, "-y", "-i", a1_b1_file, "-i", a1b1_file, "-i", a1_file,
                    "-map", "1:v", "-map", "0:v", "-map", "2:a", "-c", "copy",
                    "-map_metadata", "-1", "-disposition:v:0", "forced",
                    "-disposition:v:1", "default", output_file
                ]
                self.run_cmd(cmd5)
                
                self.log(f"处理完成：{output_file}")

                # 删除源文件（如果启用）
                if del_a and os.path.exists(main_file):
                    try:
                        os.remove(main_file)
                        self.log(f"已删除源文件：{main_file}")
                    except Exception as e:
                        self.log(f"删除源文件失败：{e}")
                
                if del_b and os.path.exists(background_file):
                    try:
                        os.remove(background_file)
                        self.log(f"已删除背景文件：{background_file}")
                    except Exception as e:
                        self.log(f"删除背景文件失败：{e}")
                
        except Exception as e:
            self.log(f"处理失败：{e}")
            raise

    def run_processing_logic(self, video_a_list, video_b_list, output_dir_path, is_batch, del_a, del_b):
        """主要的处理逻辑入口"""
        try:
            if is_batch:
                # 批量模式
                if not video_a_list or not video_b_list:
                    self.log("错误：批量模式下需要提供主视频文件夹和背景视频文件夹")
                    return
                
                # 获取文件夹中的所有视频文件
                video_extensions = ('.mp4', '.mkv', '.mov', '.avi')
                main_files = [f for f in os.listdir(video_a_list[0]) if f.lower().endswith(video_extensions)]
                background_files = [f for f in os.listdir(video_b_list[0]) if f.lower().endswith(video_extensions)]
                
                main_files.sort()
                background_files.sort()
                
                if not main_files:
                    self.log("错误：主视频文件夹中没有找到视频文件！")
                    return
                
                if not background_files:
                    self.log("错误：背景视频文件夹中没有找到视频文件！")
                    return
                
                self.log(f"找到 {len(main_files)} 个主视频文件，{len(background_files)} 个背景视频文件，开始批量处理...")
                
                # 确保一对一匹配
                for idx, main_file in enumerate(main_files):
                    if idx >= len(background_files):
                        self.log(f"警告：背景视频数量不足，跳过剩余的主视频")
                        break
                    
                    main_path = os.path.join(video_a_list[0], main_file)
                    background_path = os.path.join(video_b_list[0], background_files[idx])
                    
                    basename = os.path.splitext(main_file)[0]
                    self.log(f"[{idx+1}/{len(main_files)}] 处理文件：{main_file}")
                    
                    try:
                        self.process_single_video(main_path, background_path, output_dir_path, del_a, del_b)
                    except Exception as e:
                        self.log(f"❌ 失败：{main_file}，原因：{e}")
                        continue
                
                self.log(f"离火AB批量处理完成")
                
            else:
                # 单文件模式
                if not video_a_list or not video_b_list:
                    self.log("错误：请选择主视频和背景视频")
                    return
                
                main_file = video_a_list[0]
                background_file = video_b_list[0]
                
                self.log("离火AB处理中请等待")
                self.process_single_video(main_file, background_file, output_dir_path, del_a, del_b)
                self.log("离火AB处理完成")
                
        except Exception as e:
            self.log(f"离火AB处理失败：{e}")
            raise 