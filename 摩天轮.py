# Decompiled with PyLingual (https://pylingual.io)
# Internal filename: 视频.py
# Bytecode version: 3.12.0rc2 (3531)
# Source timestamp: 1970-01-01 00:00:00 UTC (0)

import subprocess
import json
import os
import sys
import tempfile
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, QWidget, QLabel, QTextEdit, QFileDialog, QProgressBar, QMessageBox, QFrame, QSizePolicy, QComboBox, QCheckBox
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QPainter, QLinearGradient, QColor, QPen, QIcon, QPixmap
import base64
try:
    from logo import imgBase64
except ImportError:
    imgBase64 = None

def set_local_app_icon(app):
    """设置本地文件图标的备用函数"""
    try:
        import os
        icon_paths = ['luffy.ico', 'luffy.png', 'icon.ico', 'icon.png']
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    app.setWindowIcon(icon)
                    print(f'✅ 已设置应用图标: {icon_path}')
                    return True
        print('⚠️ 未找到本地图标文件')
        return False
    except Exception as e:
        print(f'设置本地图标失败: {str(e)}')
        return False

def set_luffy_icon(window):
    """为窗口设置路飞图标"""
    try:
        import os
        if imgBase64:
            image_data = base64.b64decode(imgBase64)
            pixmap = QPixmap()
            pixmap.loadFromData(image_data)
            if not pixmap.isNull():
                icon = QIcon(pixmap)
                window.setWindowIcon(icon)
                return True
        icon_paths = ['luffy.ico', 'luffy.png', 'icon.ico', 'icon.png']
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                icon = QIcon(icon_path)
                if not icon.isNull():
                    window.setWindowIcon(icon)
                    return True
        return False
    except Exception as e:
        print(f'设置图标失败: {str(e)}')
        return False

class GradientLabel(QLabel):
    """自定义渐变字体标签"""

    def __init__(self, text='', parent=None):
        super().__init__(text, parent)
        self.gradient_colors = [QColor(131, 58, 180), QColor(253, 29, 29), QColor(252, 176, 69)]

    def paintEvent(self, event):
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)
            gradient = QLinearGradient(0, 0, self.width(), 0)
            gradient.setColorAt(0.0, self.gradient_colors[0])
            gradient.setColorAt(0.5, self.gradient_colors[1])
            gradient.setColorAt(1.0, self.gradient_colors[2])
            pen = QPen()
            pen.setBrush(gradient)
            pen.setWidth(2)
            painter.setPen(pen)
            font = self.font()
            painter.setFont(font)
            painter.drawText(self.rect(), self.alignment(), self.text())
            painter.setPen(QPen(QColor(255, 0, 255, 50), 4))
            painter.drawText(self.rect(), self.alignment(), self.text())

class VideoProcessor(QThread):
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(bool, str)
    task_finished_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.video_folder = ''
        self.image_folder = ''
        self.output_dir = 'output'
        self.running = False
        self.current_task = ''
        self.total_tasks = 0
        self.completed_tasks = 0
        self.processing_mode = 'high_quality'
        self.use_gpu = False
        self.gpu_available = False
        self.gpu_type = 'none'
        self.current_process = None

    def set_processing_mode(self, mode):
        """设置处理模式：现在只有高质量模式"""
        self.processing_mode = 'high_quality'

    def set_gpu_acceleration(self, use_gpu):
        """设置是否使用GPU加速"""
        self.use_gpu = use_gpu

    def check_gpu_support(self):
        """检查NVIDIA GPU和AMD GPU硬件加速支持"""
        try:
            ffmpeg_path, _ = self.get_ffmpeg_path()
            cmd = [ffmpeg_path, '-encoders']
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
            output = result.stdout
            if 'h264_nvenc' in output and self._test_nvenc_encoding():
                self.gpu_available = True
                self.gpu_type = 'nvidia'
                self.progress_signal.emit(0, '检测到NVIDIA显卡，可启用高速模式')
                return True
            if 'h264_amf' in output and self._test_amf_encoding():
                self.gpu_available = True
                self.gpu_type = 'amd'
                self.progress_signal.emit(0, '检测到AMD显卡，可启用高速模式')
                return True
        except Exception as e:
            pass
        self.gpu_available = False
        self.gpu_type = 'none'
        self.progress_signal.emit(0, '使用标准处理模式')
        return False

    def _test_nvenc_encoding(self):
        """测试NVENC编码器是否真正可用"""
        try:
            ffmpeg_path, _ = self.get_ffmpeg_path()
            cmd = [ffmpeg_path, '-f', 'lavfi', '-i', 'testsrc=duration=1:size=320x240:rate=1', '-c:v', 'h264_nvenc', '-preset', 'fast', '-rc', 'constqp', '-qp', '18', '-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709', '-frames:v', '5', '-f', 'null', '-']
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=20, creationflags=subprocess.CREATE_NO_WINDOW)
            if result.returncode == 0:
                self.progress_signal.emit(0, '✅ NVENC编码器测试成功，支持色彩处理')
                return True
            stderr = result.stderr.decode('utf-8', errors='ignore')
            if 'driver' in stderr.lower():
                self.progress_signal.emit(0, '❌ GPU驱动版本过旧，可能导致绿屏问题')
                return False
            if 'not supported' in stderr.lower():
                self.progress_signal.emit(0, '❌ 显卡型号不支持NVENC')
                return False
            if 'busy' in stderr.lower() or 'occupied' in stderr.lower():
                self.progress_signal.emit(0, '⚠️ GPU被其他程序占用')
                return False
            if 'color' in stderr.lower() or 'pixel' in stderr.lower():
                self.progress_signal.emit(0, '⚠️ GPU色彩处理异常，建议使用CPU模式')
                return False
            self.progress_signal.emit(0, f'❌ NVENC测试失败: {stderr[:100]}')
            return False
        except subprocess.TimeoutExpired:
            self.progress_signal.emit(0, '⏱️ GPU测试超时，可能性能不足')
            return False
        except Exception as e:
            self.progress_signal.emit(0, f'🚨 GPU测试异常: {str(e)}')
            return False

    def _test_amf_encoding(self):
        """测试AMD AMF编码器是否真正可用"""
        try:
            ffmpeg_path, _ = self.get_ffmpeg_path()
            cmd = [ffmpeg_path, '-f', 'lavfi', '-i', 'testsrc=duration=1:size=320x240:rate=1', '-c:v', 'h264_amf', '-quality', 'speed', '-rc', 'cqp', '-qp_i', '18', '-qp_p', '18', '-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709', '-frames:v', '5', '-f', 'null', '-']
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=20, creationflags=subprocess.CREATE_NO_WINDOW)
            if result.returncode == 0:
                self.progress_signal.emit(0, '✅ AMD AMF编码器测试成功，支持色彩处理')
                return True
            stderr = result.stderr.decode('utf-8', errors='ignore')
            if 'driver' in stderr.lower():
                self.progress_signal.emit(0, '❌ AMD驱动版本过旧，可能导致编码问题')
                return False
            if 'not supported' in stderr.lower():
                self.progress_signal.emit(0, '❌ 显卡型号不支持AMF')
                return False
            if 'busy' in stderr.lower() or 'occupied' in stderr.lower():
                self.progress_signal.emit(0, '⚠️ GPU被其他程序占用')
                return False
            if 'color' in stderr.lower() or 'pixel' in stderr.lower():
                self.progress_signal.emit(0, '⚠️ AMD GPU色彩处理异常，建议使用CPU模式')
                return False
            self.progress_signal.emit(0, f'❌ AMF测试失败: {stderr[:100]}')
            return False
        except subprocess.TimeoutExpired:
            self.progress_signal.emit(0, '⏱️ AMD GPU测试超时，可能性能不足')
            return False
        except Exception as e:
            self.progress_signal.emit(0, f'🚨 AMD GPU测试异常: {str(e)}')
            return False

    def get_detailed_gpu_info(self):
        """获取详细的GPU诊断信息"""
        try:
            ffmpeg_path, _ = self.get_ffmpeg_path()
            version_cmd = [ffmpeg_path, '-version']
            version_result = subprocess.run(version_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
            version_info = version_result.stdout if version_result.returncode == 0 else '获取版本失败'
            encoders_cmd = [ffmpeg_path, '-encoders']
            encoders_result = subprocess.run(encoders_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
            encoders_info = encoders_result.stdout if encoders_result.returncode == 0 else '获取编码器失败'
            nvenc_available = 'h264_nvenc' in encoders_info
            amf_available = 'h264_amf' in encoders_info
            return {'version': version_info, 'encoders': encoders_info, 'nvenc_available': nvenc_available, 'nvenc_working': self._test_nvenc_encoding() if nvenc_available else False, 'amf_available': amf_available, 'amf_working': self._test_amf_encoding() if amf_available else False}
        except Exception as e:
            return {'error': f'诊断失败: {str(e)}', 'nvenc_available': False, 'nvenc_working': False, 'amf_available': False, 'amf_working': False}

    def get_video_encoder(self):
        """根据GPU设置返回相应的视频编码器"""
        if self.use_gpu and self.gpu_available:
            if self.gpu_type == 'nvidia':
                return 'h264_nvenc'
            if self.gpu_type == 'amd':
                return 'h264_amf'
        return 'libx264'

    def get_encoder_preset(self):
        """根据编码器返回相应的预设"""
        if self.use_gpu and self.gpu_available:
            if self.gpu_type == 'nvidia':
                return 'fast'
            if self.gpu_type == 'amd':
                return 'speed'
        return 'ultrafast'

    def get_quality_params(self):
        """根据编码器返回相应的质量参数"""
        if self.use_gpu and self.gpu_available:
            if self.gpu_type == 'nvidia':
                return ['-rc', 'constqp', '-qp', '18', '-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709']
            if self.gpu_type == 'amd':
                return ['-rc', 'cqp', '-qp_i', '18', '-qp_p', '18', '-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709']
        cpu_count = os.cpu_count()
        return ['-crf', '20', '-preset', 'faster', '-threads', str(cpu_count), '-tune', 'fastdecode', '-movflags', '+faststart', '-x264-params', f'threads={cpu_count}:sliced-threads=1']

    def get_decode_params(self):
        """获取硬件解码参数 - 条件性启用"""
        return []

    def get_ffmpeg_path(self):
        """获取FFmpeg和FFprobe的路径"""
        if getattr(sys, 'frozen', False):
            base_path = os.path.dirname(sys.executable)
            ffmpeg_path = os.path.join(base_path, 'ffmpeg/ffmpeg.exe')
            ffprobe_path = os.path.join(base_path, 'ffmpeg/ffprobe.exe')
        else:
            ffmpeg_path = 'ffmpeg/ffmpeg.exe'
            ffprobe_path = 'ffmpeg/ffprobe.exe'
        if not os.path.exists(ffmpeg_path):
            error_msg = f'未找到FFmpeg: {ffmpeg_path}\n请确保_ffmpeg.exe在_internal目录下'
            raise FileNotFoundError(error_msg)
        if not os.path.exists(ffprobe_path):
            error_msg = f'未找到FFprobe: {ffprobe_path}\n请确保_ffprobe.exe在_internal目录下'
            raise FileNotFoundError(error_msg)
        return (ffmpeg_path, ffprobe_path)

    def get_video_metadata(self, input_file):
        """获取视频时长和分辨率"""
        _, ffprobe_path = self.get_ffmpeg_path()
        try:
            input_file = self._ensure_unicode_path(input_file)
            cmd = [ffprobe_path, '-v', 'error', '-show_entries', 'stream=width,height,duration', '-of', 'json', input_file]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, universal_newlines=False)

            try:
                stdout = result.stdout.decode('utf-8')
            except UnicodeDecodeError:
                self.progress_signal.emit(0, '无法读取视频参数，使用默认设置')
                return {'width': 1080, 'height': 1920, 'duration': 2.0}

            try:
                metadata = json.loads(stdout)
                stream = metadata['streams'][0]
                width = int(stream.get('width', 1080))
                height = int(stream.get('height', 1920))
                duration = float(stream.get('duration', 2.0))
                return {'width': width, 'height': height, 'duration': duration}
            except (json.JSONDecodeError, IndexError, KeyError, ValueError) as e:
                self.progress_signal.emit(0, '视频参数分析失败，使用默认设置')
                return {'width': 1080, 'height': 1920, 'duration': 2.0}

        except subprocess.CalledProcessError as e:
            self.progress_signal.emit(0, '视频分析失败，使用默认配置')
            return {'width': 1080, 'height': 1920, 'duration': 2.0}

    def run_ffmpeg(self, cmd, progress_start, progress_end, total_frames=None):
        """运行FFmpeg命令并跟踪进度"""
        ffmpeg_path, _ = self.get_ffmpeg_path()
        cmd[0] = ffmpeg_path
        encoded_cmd = [self._ensure_unicode_path(arg) for arg in cmd]
        self.progress_signal.emit(progress_start, '正在执行处理任务...')
        try:
            process = subprocess.Popen(encoded_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False, creationflags=subprocess.CREATE_NO_WINDOW)
            error_output = []
            current_frame = 0
            while True:
                if not self.running:
                    process.terminate()
                output = process.stderr.readline()
                if output == b'' and process.poll() is not None:
                    break
                if output:
                    try:
                        decoded_output = output.decode('utf-8').strip()
                    except UnicodeDecodeError:
                        continue
                    error_output.append(decoded_output)
                    if 'frame=' in decoded_output:
                        try:
                            frame_info = decoded_output.split('frame=')[1].split('fps=')[0].strip()
                            current_frame = int(frame_info)
                            if total_frames:
                                progress = progress_start + int(current_frame / total_frames * (progress_end - progress_start))
                                self.progress_signal.emit(progress, f'处理进度: {current_frame}/{total_frames}')
                        except (IndexError, ValueError):
                            pass
            if process.returncode != 0:
                error_text = '\n'.join(error_output[-10:])
                if 'cuda' in error_text.lower() or 'nvenc' in error_text.lower() or 'amf' in error_text.lower():
                    error_msg = 'GPU加速失败: 显卡驱动或硬件不支持'
                    self.progress_signal.emit(0, error_msg)
                else:
                    if 'invalid' in error_text.lower() or 'unsupported' in error_text.lower():
                        error_msg = '文件格式不支持: 请检查视频/图片格式'
                        self.progress_signal.emit(0, error_msg)
                    else:
                        if 'permission' in error_text.lower() or 'access' in error_text.lower():
                            error_msg = '文件访问失败: 请检查文件权限或路径'
                            self.progress_signal.emit(0, error_msg)
                        else:
                            if 'memory' in error_text.lower() or 'resource' in error_text.lower():
                                error_msg = '内存不足: 请关闭其他程序后重试'
                                self.progress_signal.emit(0, error_msg)
                            else:
                                error_lines = [line for line in error_output if line.strip() and (not line.startswith('frame='))]
                                if error_lines:
                                    actual_error = error_lines[-1][:100]
                                    error_msg = f'处理失败: {actual_error}'
                                else:
                                    error_msg = '处理失败: 未知错误'
                                self.progress_signal.emit(0, error_msg)
                raise subprocess.CalledProcessError(process.returncode, encoded_cmd, error_msg)
        except Exception as e:
            error_msg = f'处理异常: {str(e)}'
            self.progress_signal.emit(0, error_msg)
            return (False, 0)

    def run(self):
        """线程主函数"""
        self.running = True
        self.completed_tasks = 0
        try:
            if self.use_gpu:
                self.check_gpu_support()
                if not self.gpu_available:
                    self.progress_signal.emit(0, '切换到标准处理模式')
                    self.use_gpu = False
            self._prepare_tasks()
            processing_info = '高速处理模式' if self.use_gpu and self.gpu_available else '标准处理模式'
            self.progress_signal.emit(10, f'准备处理 {self.total_tasks} 个任务，使用 {processing_info}')
            for video_path, image_path in self._match_files():
                if not self.running:
                    break
                video_name = os.path.basename(video_path)
                image_name = os.path.basename(image_path)
                self.current_task = f'{video_name} + {image_name}'
                self.progress_signal.emit(0, f'\n开始处理任务: {self.current_task}')
                try:
                    if not os.path.exists(video_path) or not os.path.exists(image_path):
                        raise FileNotFoundError('文件不存在')
                    metadata = self.get_video_metadata(video_path)
                    self.progress_signal.emit(15, '正在分析视频参数...')
                    success = False
                    original_use_gpu = self.use_gpu
                    original_gpu_available = self.gpu_available
                    original_gpu_type = self.gpu_type
                    try:
                        self._process_task_high_quality(video_path, image_path, metadata)
                        success = True
                    except Exception as gpu_error:
                        pass
                    if success:
                        self.completed_tasks += 1
                        self.task_finished_signal.emit(self.current_task)
                        self.use_gpu = original_use_gpu
                        self.gpu_available = original_gpu_available
                        self.gpu_type = original_gpu_type
                except Exception as e:
                    self.progress_signal.emit(0, f'处理任务失败: {str(e)}')
            if self.completed_tasks > 0:
                self.finished_signal.emit(True, f'所有任务处理完成！共处理 {self.completed_tasks} 个任务')
            else:
                self.finished_signal.emit(False, '没有成功处理任何任务')
        except Exception as e:
            self.finished_signal.emit(False, f'处理过程中发生错误: {str(e)}')
        finally:
            self.running = False

    def stop(self):
        """停止处理"""
        try:
            self.running = False
            if self.current_process and self.current_process.poll() is None:
                try:
                    self.progress_signal.emit(0, '正在强制停止FFmpeg进程...')
                    self.current_process.terminate()
                    self.current_process.wait(timeout=3)
                    self.current_process = None
                except subprocess.TimeoutExpired:
                    try:
                        self.current_process.kill()
                        self.current_process.wait(timeout=2)
                        self.progress_signal.emit(0, 'FFmpeg进程已强制终止')
                    except:
                        self.progress_signal.emit(0, '无法终止FFmpeg进程，请手动结束')
        except Exception as e:
            self.progress_signal.emit(0, f'停止进程时出错: {str(e)}')

    def _prepare_tasks(self):
        """基于文件修改时间排序后自动匹配"""
        if not self.video_folder or not self.image_folder:
            raise ValueError('请先选择视频文件夹和图片文件夹')
        videos = self._get_timed_files(self.video_folder, is_video=True)
        images = self._get_timed_files(self.image_folder, is_video=False)
        if not videos:
            raise ValueError('视频文件夹中没有找到有效的视频文件')
        if not images:
            raise ValueError('图片文件夹中没有找到有效的图片文件')
        videos.sort(key=lambda x: x['mtime'])
        images.sort(key=lambda x: x['mtime'])
        min_count = min(len(videos), len(images))
        self._matched_files = [(videos[i]['path'], images[i]['path']) for i in range(min_count)]
        self._match_details = []
        for i, (video_path, image_path) in enumerate(self._matched_files):
            video_info = None
            image_info = None
            for v in videos:
                if v['path'] == video_path:
                    video_info = v
                    break
            for img in images:
                if img['path'] == image_path:
                    image_info = img
                    break
            if video_info and image_info:
                v_time = self._format_time(video_info['mtime'])
                img_time = self._format_time(image_info['mtime'])
                time_diff = abs(video_info['mtime'] - image_info['mtime'])
                self._match_details.append(f"匹配 #{i + 1}: 视频 \'{os.path.basename(video_info['path'])}\' ({v_time}) ↔ 图片 \'{os.path.basename(image_info['path'])}\' ({img_time}) (时间差: {time_diff:.1f}秒)")
            else:
                self.progress_signal.emit(0, f'无法找到匹配文件信息: #{i + 1}')
        self.total_tasks = min_count
        self.progress_signal.emit(5, f'找到 {self.total_tasks} 对按时间匹配的文件')
        for i, detail in enumerate(self._match_details):
            self.progress_signal.emit(5, f'匹配 #{i + 1}: 已配对文件')
        output_dir = self._ensure_unicode_path(self.output_dir)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            self.progress_signal.emit(8, '准备输出目录')

    def _get_timed_files(self, folder, is_video=False):
        """获取带修改时间的文件列表"""
        valid_ext = ('.mp4', '.avi', '.mov') if is_video else ('.jpg', '.jpeg', '.png', '.bmp')
        files = []
        folder = self._ensure_unicode_path(folder)
        try:
            for filename in os.listdir(folder):
                filepath = os.path.join(folder, filename)
                if os.path.isfile(filepath) and filename.lower().endswith(valid_ext):
                    try:
                        mtime = os.path.getmtime(filepath)
                        files.append({'path': filepath, 'mtime': mtime, 'name': filename})
                    except Exception as e:
                        self.progress_signal.emit(0, f'获取文件时间戳失败: {filepath}, 错误: {str(e)}')
        except Exception as e:
            self.progress_signal.emit(0, f'读取文件夹失败: {folder}, 错误: {str(e)}')
        return files

    def _ensure_unicode_path(self, path):
        """确保路径为Unicode编码，支持中文和特殊符号"""
        if isinstance(path, bytes):
            return path.decode('utf-8', errors='replace')
        return str(path)

    def _format_time(self, timestamp):
        """格式化时间戳为可读格式"""
        import datetime
        return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

    def _match_files(self):
        """生成匹配的视频和图片路径对"""
        for video_path, image_path in self._matched_files:
            yield (video_path, image_path)

    def _process_task_high_quality(self, video_path, image_path, metadata):
            """高质量模式：处理单个视频-图片任务（带分辨率适配和GPU加速）"""
            video_path = self._ensure_unicode_path(video_path)
            image_path = self._ensure_unicode_path(image_path)
            temp_dir = tempfile.mkdtemp(prefix='video_process_')
            temp_dir = self._ensure_unicode_path(temp_dir)
            try:
                video_name = os.path.splitext(os.path.basename(video_path))[0]
                image_name = os.path.splitext(os.path.basename(image_path))[0]
                output_dir = self._ensure_unicode_path(self.output_dir)
                output_file = os.path.join(output_dir, f'{video_name}.mp4')
                processing_name = '高速处理' if self.use_gpu and self.gpu_available else '标准处理'
                self.progress_signal.emit(20, f'开始处理: {os.path.basename(output_file)} (高质量模式 - {processing_name})')
                encoder = self.get_video_encoder()
                preset = self.get_encoder_preset()
                video_120fps = os.path.join(temp_dir, 'video_120fps.mp4')
                quality_params = self.get_quality_params()
                decode_params = self.get_decode_params()
                cpu_count = os.cpu_count()
                cmd = ['ffmpeg', '-threads', str(cpu_count)]
                cmd.extend(decode_params)
                cmd.extend(['-i', video_path, '-filter_threads', str(cpu_count), '-r', '120', '-s', f"{metadata['width']}x{metadata['height']}", '-c:v', encoder, '-preset', 'medium' if encoder == 'h264_nvenc' else 'faster'])
                if self.use_gpu and self.gpu_available:
                    cmd.extend(['-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709'])
                cmd.extend(quality_params)
                cmd.extend(['-c:a', 'aac', '-b:a', '192k', '-threads', '0', '-y', video_120fps])
                success, _ = self.run_ffmpeg(cmd, 20, 40)
                if not success:
                    raise RuntimeError('第一阶段优化失败')
                image_video = os.path.join(temp_dir, 'image_video.mp4')
                cmd = ['ffmpeg', '-threads', str(cpu_count)]
                cmd.extend(decode_params)
                cmd.extend(['-loop', '1', '-i', image_path, '-filter_threads', str(cpu_count), '-t', str(metadata['duration']), '-r', '120', '-s', f"{metadata['width']}x{metadata['height']}", '-c:v', encoder, '-preset', 'medium' if encoder == 'h264_nvenc' else 'faster'])
                if self.use_gpu and self.gpu_available:
                    cmd.extend(['-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709'])
                cmd.extend(quality_params)
                cmd.extend(['-threads', '0', '-y', image_video])
                success, _ = self.run_ffmpeg(cmd, 40, 60)
                if not success:
                    raise RuntimeError('第二阶段优化失败')
                cmd = ['ffmpeg', '-threads', str(cpu_count)]
                cmd.extend(decode_params)
                cmd.extend(['-i', video_120fps, '-i', image_video, '-filter_threads', str(cpu_count), '-filter_complex', f"[0:v]scale={metadata['width']}:{metadata['height']},setsar=1,format=yuv420p[v0];[1:v]scale={metadata['width']}:{metadata['height']},setsar=1,format=yuv420p[v1];[v1]select=\'gte(n,2)*not(mod(n-2,2))\'[a];[v0]select=\'lt(n,2)+gte(n,2)*mod(n-2,2)\'[b];[a][b]interleave[v]", '-map', '[v]', '-map', '0:a?', '-c:v', encoder, '-preset', 'medium' if encoder == 'h264_nvenc' else 'faster'])
                if self.use_gpu and self.gpu_available:
                    if self.gpu_type == 'nvidia':
                        cmd.extend(['-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709', '-rc', 'constqp', '-qp', '18'])
                    else:
                        if self.gpu_type == 'amd':
                            cmd.extend(['-pix_fmt', 'yuv420p', '-color_range', 'tv', '-colorspace', 'bt709', '-color_primaries', 'bt709', '-color_trc', 'bt709', '-rc', 'cqp', '-qp_i', '18', '-qp_p', '18'])
                else:
                    cmd.extend(quality_params)
                cmd.extend(['-bf', '0', '-r', '120', '-threads', '0', '-y', output_file])
                if not self.run_ffmpeg(cmd, 60, 100):
                    raise RuntimeError('第三阶段合成失败')
                self.progress_signal.emit(100, f'任务完成: {os.path.basename(output_file)}')
            finally:
                pass
            self._cleanup_temp_dir(temp_dir)

    def _cleanup_temp_dir(self, temp_dir):
        """安全清理临时目录，添加重试机制"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                temp_dir = self._ensure_unicode_path(temp_dir)
                if os.path.exists(temp_dir):
                    for root, dirs, files in os.walk(temp_dir, topdown=False):
                        for name in files:
                            file_path = os.path.join(root, name)
                            file_path = self._ensure_unicode_path(file_path)
                            os.remove(file_path)
                    os.rmdir(temp_dir)
                return True
            except Exception as e:
                self.progress_signal.emit(0, f'清理临时文件失败 (尝试 {attempt + 1}/{max_retries})')
                import time
                time.sleep(0.5)

        self.progress_signal.emit(0, '无法完成清理工作')
        return False

class VideoProcessorV2(QThread):
    """低配电脑专用版本的视频处理器"""
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(bool, str)
    task_finished_signal = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.video_folder = ''
        self.image_folder = ''
        self.output_dir = 'output'
        self.running = False
        self.current_task = ''
        self.total_tasks = 0
        self.completed_tasks = 0
        self.processing_mode = 'high_quality'
        self.current_process = None

    def set_processing_mode(self, mode):
        """设置处理模式：固定为high_quality模式"""
        self.processing_mode = 'high_quality'

    def get_ffmpeg_path(self):
            """获取FFmpeg和FFprobe的路径"""
            if getattr(sys, 'frozen', False):
                base_path = os.path.dirname(sys.executable)
                ffmpeg_path = os.path.join(base_path, 'ffmpeg/ffmpeg.exe')
                ffprobe_path = os.path.join(base_path, 'ffmpeg/ffprobe.exe')
            else:
                ffmpeg_path = 'ffmpeg/ffmpeg.exe'
                ffprobe_path = 'ffmpeg/ffprobe.exe'
            if not os.path.exists(ffmpeg_path):
                error_msg = f'未找到FFmpeg: {ffmpeg_path}\n请确保_ffmpeg.exe在_internal目录下'
                raise FileNotFoundError(error_msg)
            if not os.path.exists(ffprobe_path):
                error_msg = f'未找到FFprobe: {ffprobe_path}\n请确保_ffprobe.exe在_internal目录下'
                raise FileNotFoundError(error_msg)
            return (ffmpeg_path, ffprobe_path)

    def get_video_metadata(self, input_file):
        """获取视频时长和分辨率"""
        _, ffprobe_path = self.get_ffmpeg_path()
        try:
            input_file = self._ensure_unicode_path(input_file)
            cmd = [ffprobe_path, '-v', 'error', '-show_entries', 'stream=width,height,duration', '-of', 'json', input_file]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, universal_newlines=False)

            try:
                stdout = result.stdout.decode('utf-8')
            except UnicodeDecodeError:
                self.progress_signal.emit(0, '无法读取视频参数，使用默认设置')
                return {'width': 1080, 'height': 1920, 'duration': 2.0}

            try:
                metadata = json.loads(stdout)
                stream = metadata['streams'][0]
                width = int(stream.get('width', 1080))
                height = int(stream.get('height', 1920))
                duration = float(stream.get('duration', 2.0))
                return {'width': width, 'height': height, 'duration': duration}
            except (json.JSONDecodeError, IndexError, KeyError, ValueError) as e:
                self.progress_signal.emit(0, f'解析视频元数据失败: {str(e)}')
                return {'width': 1080, 'height': 1920, 'duration': 2.0}

        except subprocess.CalledProcessError as e:
            self.progress_signal.emit(0, f'获取视频元数据失败 (错误码 {e.returncode})')
            return {'width': 1080, 'height': 1920, 'duration': 2.0}
        except Exception as e:
            self.progress_signal.emit(0, f'获取视频元数据失败: {str(e)}')
            return {'width': 1080, 'height': 1920, 'duration': 2.0}

    def run_ffmpeg(self, cmd, progress_start, progress_end, total_frames=None):
        """运行FFmpeg命令并跟踪进度"""
        ffmpeg_path, _ = self.get_ffmpeg_path()
        cmd[0] = ffmpeg_path
        encoded_cmd = [self._ensure_unicode_path(arg) for arg in cmd]
        self.progress_signal.emit(progress_start, f"开始执行: {' '.join(encoded_cmd[:3])}...")
        try:
            process = subprocess.Popen(encoded_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=False, creationflags=subprocess.CREATE_NO_WINDOW)
            self.current_process = process
            error_output = []
            current_frame = 0

            while True:
                if not self.running:
                    process.terminate()
                    break
                output = process.stderr.readline()
                if output == b'' and process.poll() is not None:
                    break
                if output:
                    try:
                        decoded_output = output.decode('utf-8').strip()
                    except UnicodeDecodeError:
                        continue
                    error_output.append(decoded_output)
                    if 'frame=' in decoded_output:
                        try:
                            frame_info = decoded_output.split('frame=')[1].split('fps=')[0].strip()
                            current_frame = int(frame_info)
                            if total_frames:
                                progress = progress_start + int(current_frame / total_frames * (progress_end - progress_start))
                                self.progress_signal.emit(progress, f'处理中: 帧 {current_frame}/{total_frames}')
                        except (IndexError, ValueError):
                            pass

            if process.returncode != 0:
                error_msg = '\n'.join(error_output[-5:])
                raise subprocess.CalledProcessError(process.returncode, encoded_cmd, error_msg)

            process.stdout.close()
            process.stderr.close()
            return (True, current_frame)

        except subprocess.CalledProcessError as e:
            self.progress_signal.emit(0, f'FFmpeg执行失败 (错误码 {e.returncode})')
            return (False, 0)
        except Exception as e:
            self.progress_signal.emit(0, f'执行FFmpeg命令时出错: {str(e)}')
            return (False, 0)

    def run(self):
        """线程主函数"""
        self.running = True
        self.completed_tasks = 0
        try:
            self._prepare_tasks()
            self.progress_signal.emit(10, f'准备处理 {self.total_tasks} 个任务，模式: {self.processing_mode}')

            for video_path, image_path in self._match_files():
                if not self.running:
                    break

                video_name = os.path.basename(video_path)
                image_name = os.path.basename(image_path)
                self.current_task = f'{video_name} + {image_name}'
                self.progress_signal.emit(0, f'\n开始处理任务: {self.current_task}')

                try:
                    if not os.path.exists(video_path) or not os.path.exists(image_path):
                        raise FileNotFoundError('文件不存在')
                    metadata = self.get_video_metadata(video_path)
                    self.progress_signal.emit(15, f"视频分辨率: {metadata['width']}x{metadata['height']}, 时长: {metadata['duration']:.2f}秒")
                    self._process_task_high_quality(video_path, image_path, metadata)
                    self.completed_tasks += 1
                    self.task_finished_signal.emit(self.current_task)
                except Exception as e:
                    self.progress_signal.emit(0, f'处理任务失败: {str(e)}')
                    self.task_finished_signal.emit(f'{self.current_task} (失败)')

            if self.completed_tasks > 0:
                self.finished_signal.emit(True, f'所有任务处理完成！共处理 {self.completed_tasks} 个任务')
            else:
                self.finished_signal.emit(False, '没有成功处理任何任务')

        except Exception as e:
            self.finished_signal.emit(False, f'处理过程中发生错误: {str(e)}')
        finally:
            self.running = False

    def stop(self):
            """停止处理"""
            try:
                self.running = False
                if self.current_process and self.current_process.poll() is None:
                    try:
                        self.progress_signal.emit(0, '正在强制停止FFmpeg进程...')
                        self.current_process.terminate()
                        self.current_process.wait(timeout=3)
                        self.current_process = None
                    except subprocess.TimeoutExpired:
                        try:
                            self.current_process.kill()
                            self.current_process.wait(timeout=2)
                            self.progress_signal.emit(0, 'FFmpeg进程已强制终止')
                        except:
                            self.progress_signal.emit(0, '无法终止FFmpeg进程，请手动结束')
            except Exception as e:
                self.progress_signal.emit(0, f'停止进程时出错: {str(e)}')

    def _prepare_tasks(self):
        """基于文件修改时间排序后自动匹配"""
        if not self.video_folder or not self.image_folder:
            raise ValueError('请先选择视频文件夹和图片文件夹')
        videos = self._get_timed_files(self.video_folder, is_video=True)
        images = self._get_timed_files(self.image_folder, is_video=False)
        if not videos:
            raise ValueError('视频文件夹中没有找到有效的视频文件')
        if not images:
            raise ValueError('图片文件夹中没有找到有效的图片文件')
        videos.sort(key=lambda x: x['mtime'])
        images.sort(key=lambda x: x['mtime'])
        min_count = min(len(videos), len(images))
        self._matched_files = [(videos[i]['path'], images[i]['path']) for i in range(min_count)]
        self._match_details = []
        for i, (v, img) in enumerate(self._matched_files):
            if not isinstance(v, dict) or not isinstance(img, dict):
                self.progress_signal.emit(0, f'文件信息格式错误: 视频#{i + 1}或图片#{i + 1}')
            else:
                if 'mtime' not in v or 'mtime' not in img:
                    self.progress_signal.emit(0, f'文件时间戳缺失: 视频#{i + 1}或图片#{i + 1}')
                else:
                    if 'path' not in v or 'path' not in img:
                        self.progress_signal.emit(0, f'文件路径缺失: 视频#{i + 1}或图片#{i + 1}')
                    else:
                        v_time = self._format_time(v['mtime'])
                        img_time = self._format_time(img['mtime'])
                        time_diff = abs(v['mtime'] - img['mtime'])
                        self._match_details.append(f"匹配 #{i + 1}: 视频 \'{os.path.basename(v['path'])}\' ({v_time}) ↔ 图片 \'{os.path.basename(img['path'])}\' ({img_time}) (时间差: {time_diff:.1f}秒)")
        self.total_tasks = min_count
        self.progress_signal.emit(5, f'找到 {self.total_tasks} 对按时间匹配的文件')
        for detail in self._match_details:
            self.progress_signal.emit(5, detail)
        output_dir = self._ensure_unicode_path(self.output_dir)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            self.progress_signal.emit(8, f'创建输出目录: {output_dir}')

    def _get_timed_files(self, folder, is_video=False):
        """获取带修改时间的文件列表"""
        valid_ext = ('.mp4', '.avi', '.mov') if is_video else ('.jpg', '.jpeg', '.png', '.bmp')
        files = []
        folder = self._ensure_unicode_path(folder)
        try:
            for filename in os.listdir(folder):
                filepath = os.path.join(folder, filename)
                if os.path.isfile(filepath) and filename.lower().endswith(valid_ext):
                    try:
                        mtime = os.path.getmtime(filepath)
                        files.append({'path': filepath, 'mtime': mtime, 'name': filename})
                    except Exception as e:
                        self.progress_signal.emit(0, f'获取文件时间戳失败: {filepath}, 错误: {str(e)}')
        except Exception as e:
            self.progress_signal.emit(0, f'读取文件夹失败: {folder}, 错误: {str(e)}')
        return files

    def _ensure_unicode_path(self, path):
        """确保路径为Unicode编码，支持中文和特殊符号"""
        if isinstance(path, bytes):
            return path.decode('utf-8', errors='replace')
        return str(path)

    def _format_time(self, timestamp):
        """格式化时间戳为可读格式"""
        import datetime
        return datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

    def _match_files(self):
        """生成匹配的视频和图片路径对"""
        for video_path, image_path in self._matched_files:
            yield (video_path, image_path)

    def _process_task_high_quality(self, video_path, image_path, metadata):
            """高质量模式：处理单个视频-图片任务（带分辨率适配）"""
            video_path = self._ensure_unicode_path(video_path)
            image_path = self._ensure_unicode_path(image_path)
            temp_dir = tempfile.mkdtemp(prefix='video_process_')
            temp_dir = self._ensure_unicode_path(temp_dir)
            try:
                video_name = os.path.splitext(os.path.basename(video_path))[0]
                image_name = os.path.splitext(os.path.basename(image_path))[0]
                output_dir = self._ensure_unicode_path(self.output_dir)
                output_file = os.path.join(output_dir, f'{video_name}.mp4')
                self.progress_signal.emit(20, f'开始处理: {output_file} (高质量模式)')
                video_120fps = os.path.join(temp_dir, 'video_120fps.mp4')
                cpu_count = os.cpu_count()
                success, _ = self.run_ffmpeg(['ffmpeg', '-threads', str(cpu_count), '-i', video_path, '-filter_threads', str(cpu_count), '-r', '120', '-s', f"{metadata['width']}x{metadata['height']}", '-c:v', 'libx264', '-preset', 'faster', '-threads', str(cpu_count), '-tune', 'fastdecode', '-crf', '20', '-c:a', 'aac', '-b:a', '192k', '-y', video_120fps], 20, 40)
                if not success:
                    raise RuntimeError('视频转120fps失败')
                image_video = os.path.join(temp_dir, 'image_video.mp4')
                success, _ = self.run_ffmpeg(['ffmpeg', '-threads', str(cpu_count), '-loop', '1', '-i', image_path, '-filter_threads', str(cpu_count), '-t', str(metadata['duration']), '-r', '120', '-s', f"{metadata['width']}x{metadata['height']}", '-c:v', 'libx264', '-preset', 'faster', '-threads', str(cpu_count), '-tune', 'fastdecode', '-y', image_video], 40, 60)
                if not success:
                    raise RuntimeError('图片转视频失败')

                success = self.run_ffmpeg(['ffmpeg', '-threads', str(cpu_count), '-i', video_120fps, '-i', image_video, '-filter_complex', f"[0:v]scale={metadata['width']}:{metadata['height']},setsar=1[v0];[1:v]scale={metadata['width']}:{metadata['height']},setsar=1[v1];[v1]select=\'gte(n,2)*not(mod(n-2,2))\'[a];[v0]select=\'lt(n,2)+gte(n,2)*mod(n-2,2)\'[b];[a][b]interleave[v]", '-filter_threads', str(cpu_count), '-map', '[v]', '-map', '0:a?', '-c:v', 'libx264', '-preset', 'faster', '-threads', str(cpu_count), '-tune', 'fastdecode', '-bf', '0', '-pix_fmt', 'yuv420p', '-r', '120', '-movflags', '+faststart', '-y', output_file, 60, 100])
                if not success:
                    raise RuntimeError('视频合并失败')

                self.progress_signal.emit(100, f'任务完成: {output_file}')
            finally:
                self._cleanup_temp_dir(temp_dir)

    def _cleanup_temp_dir(self, temp_dir):
            """安全清理临时目录，添加重试机制"""
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    temp_dir = self._ensure_unicode_path(temp_dir)
                    if os.path.exists(temp_dir):
                        for root, dirs, files in os.walk(temp_dir, topdown=False):
                            for name in files:
                                file_path = os.path.join(root, name)
                                file_path = self._ensure_unicode_path(file_path)
                                os.remove(file_path)
                        os.rmdir(temp_dir)
                    return True
                except Exception as e:
                    self.progress_signal.emit(0, f'清理临时文件失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}')
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(0.5)

            self.progress_signal.emit(0, f'无法清理临时目录: {temp_dir}')
            return False

class VideoProcessorWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle('功能一：KKSS摩天轮独家视频处理器')
            self.setFixedSize(800, 650)
            self.use_gpu = False
            self.gpu_available = False
            self.gpu_type = 'none'
            set_luffy_icon(self)
            self.setStyleSheet('\n            QMainWindow {\n                background: #f5f6fa;\n            }\n        ')
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(12)
            header_frame = QFrame()
            header_frame.setFixedHeight(100)
            header_frame.setStyleSheet('\n            QFrame {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #667eea, stop:1 #764ba2);\n                border-radius: 12px;\n            }\n        ')
            header_layout = QVBoxLayout(header_frame)
            header_layout.setContentsMargins(25, 20, 25, 20)
            title = GradientLabel('🎬 AI视频处理器 Pro')
            title.setStyleSheet('\n            font-size: 26px; \n            font-weight: bold; \n            background: transparent;\n        ')
            title.setAlignment(Qt.AlignCenter)
            font = QFont()
            font.setPointSize(20)
            font.setBold(True)
            title.setFont(font)
            subtitle = QLabel('智能加速 · 高质量视频处理')
            subtitle.setStyleSheet('\n            font-size: 14px; \n            color: rgba(255,255,255,0.85);\n            margin-top: 4px;\n        ')
            subtitle.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title)
            header_layout.addWidget(subtitle)
            layout.addWidget(header_frame)
            config_frame = QFrame()
            config_frame.setFixedHeight(155)
            config_frame.setStyleSheet('\n            QFrame {\n                background: white;\n                border-radius: 10px;\n                border: 1px solid #e9ecef;\n            }\n        ')
            config_layout = QVBoxLayout(config_frame)
            config_layout.setContentsMargins(20, 15, 20, 15)
            config_layout.setSpacing(12)
            mode_layout = QHBoxLayout()
            mode_label = QLabel('处理模式:')
            mode_label.setStyleSheet('font-size: 15px; color: #495057; font-weight: 500; min-width: 80px;')
            mode_layout.addWidget(mode_label)
            self.mode_combo = QComboBox()
            self.mode_combo.addItems(['高质量模式优化版'])
            self.mode_combo.setStyleSheet('\n            QComboBox {\n                border: 1px solid #ddd;\n                border-radius: 6px;\n                padding: 8px 12px;\n                font-size: 14px;\n                min-width: 160px;\n                max-width: 160px;\n                background: white;\n                color: #495057;\n            }\n            QComboBox:hover {\n                border-color: #667eea;\n            }\n        ')
            self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
            mode_layout.addWidget(self.mode_combo)
            self.mode_info = QLabel('💎 高质量模式：120fps，处理时间较长')
            self.mode_info.setStyleSheet('font-size: 12px; color: #6c757d; margin-left: 12px;')
            mode_layout.addWidget(self.mode_info, 1)
            config_layout.addLayout(mode_layout)
            gpu_layout = QHBoxLayout()
            gpu_label = QLabel('NVIDIA GPU:')
            gpu_label.setStyleSheet('\n            font-size: 15px;\n            color: #2d3436;\n            font-weight: 600;\n            min-width: 80px;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            gpu_layout.addWidget(gpu_label)
            self.gpu_checkbox = QCheckBox('启用NVIDIA加速')
            self.gpu_checkbox.setStyleSheet('\n            QCheckBox {\n                font-size: 14px;\n                color: #495057;\n                font-weight: 500;\n            }\n            QCheckBox::indicator {\n                width: 18px;\n                height: 18px;\n                border-radius: 9px;\n                border: 2px solid #ddd;\n                background: white;\n            }\n            QCheckBox::indicator:checked {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #76b900, stop:1 #4caf50);\n                border: 2px solid #76b900;\n            }\n        ')
            self.gpu_checkbox.stateChanged.connect(self.on_gpu_changed)
            gpu_layout.addWidget(self.gpu_checkbox)
            self.gpu_test_btn = QPushButton('检测')
            self.gpu_test_btn.setFixedSize(50, 30)
            self.gpu_test_btn.setStyleSheet('\n            QPushButton {\n                background: #76b900;\n                color: white;\n                border: none;\n                border-radius: 15px;\n                font-size: 11px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: #5a8f00;\n            }\n        ')
            self.gpu_test_btn.clicked.connect(self.test_gpu_support)
            gpu_layout.addWidget(self.gpu_test_btn)
            self.gpu_diagnose_btn = QPushButton('诊断')
            self.gpu_diagnose_btn.setFixedSize(50, 30)
            self.gpu_diagnose_btn.setStyleSheet('\n            QPushButton {\n                background: #17a2b8;\n                color: white;\n                border: none;\n                border-radius: 15px;\n                font-size: 11px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: #138496;\n            }\n        ')
            self.gpu_diagnose_btn.clicked.connect(self.detailed_gpu_diagnosis)
            gpu_layout.addWidget(self.gpu_diagnose_btn)
            self.gpu_info = QLabel('💻 不选择显卡默认CPU标准模式')
            self.gpu_info.setStyleSheet('font-size: 12px; color: #6c757d; margin-left: 12px;')
            gpu_layout.addWidget(self.gpu_info, 1)
            config_layout.addLayout(gpu_layout)
            amd_layout = QHBoxLayout()
            amd_label = QLabel('AMD GPU:')
            amd_label.setStyleSheet('\n            font-size: 15px;\n            color: #2d3436;\n            font-weight: 600;\n            min-width: 80px;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            amd_layout.addWidget(amd_label)
            self.amd_checkbox = QCheckBox('启用AMD加速')
            self.amd_checkbox.setStyleSheet('\n            QCheckBox {\n                font-size: 14px;\n                color: #495057;\n                font-weight: 500;\n            }\n            QCheckBox::indicator {\n                width: 18px;\n                height: 18px;\n                border-radius: 9px;\n                border: 2px solid #ddd;\n                background: white;\n            }\n            QCheckBox::indicator:checked {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #ed1c24, stop:1 #ff6b6b);\n                border: 2px solid #ed1c24;\n            }\n        ')
            self.amd_checkbox.stateChanged.connect(self.on_amd_changed)
            amd_layout.addWidget(self.amd_checkbox)
            self.amd_test_btn = QPushButton('检测')
            self.amd_test_btn.setFixedSize(50, 30)
            self.amd_test_btn.setStyleSheet('\n            QPushButton {\n                background: #ed1c24;\n                color: white;\n                border: none;\n                border-radius: 15px;\n                font-size: 11px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: #c41e3a;\n            }\n        ')
            self.amd_test_btn.clicked.connect(self.test_amd_support)
            amd_layout.addWidget(self.amd_test_btn)
            self.amd_diagnose_btn = QPushButton('诊断')
            self.amd_diagnose_btn.setFixedSize(50, 30)
            self.amd_diagnose_btn.setStyleSheet('\n            QPushButton {\n                background: #17a2b8;\n                color: white;\n                border: none;\n                border-radius: 15px;\n                font-size: 11px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: #138496;\n            }\n        ')
            self.amd_diagnose_btn.clicked.connect(self.detailed_amd_diagnosis)
            amd_layout.addWidget(self.amd_diagnose_btn)
            self.amd_info = QLabel('💻 不选择显卡默认CPU标准模式')
            self.amd_info.setStyleSheet('font-size: 12px; color: #6c757d; margin-left: 12px;')
            amd_layout.addWidget(self.amd_info, 1)
            config_layout.addLayout(amd_layout)
            layout.addWidget(config_frame)
            file_frame = QFrame()
            file_frame.setFixedHeight(120)
            file_frame.setStyleSheet('\n            QFrame {\n                background: white;\n                border-radius: 10px;\n                border: 1px solid #e9ecef;\n            }\n        ')
            file_layout = QVBoxLayout(file_frame)
            file_layout.setContentsMargins(20, 15, 20, 15)
            file_layout.setSpacing(12)
            video_layout = QHBoxLayout()
            video_label = QLabel('视频文件夹:')
            video_label.setStyleSheet('\n            font-size: 15px; \n            color: #2d3436; \n            font-weight: 600; \n            min-width: 90px;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            video_layout.addWidget(video_label)
            self.video_btn = QPushButton('选择文件夹')
            self.video_btn.setFixedSize(110, 32)
            self.video_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #667eea, stop:1 #764ba2);\n                color: white;\n                border: none;\n                border-radius: 16px;\n                font-size: 13px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #5a67d8, stop:1 #6b46c1);\n            }\n        ')
            self.video_btn.clicked.connect(self.select_video_folder)
            video_layout.addWidget(self.video_btn)
            self.video_path = QLabel('尚未选择视频文件夹')
            self.video_path.setStyleSheet('\n            border: 1px solid #e9ecef;\n            border-radius: 6px;\n            padding: 8px 12px;\n            font-size: 13px;\n            color: #6c757d;\n            background: #f8f9fa;\n        ')
            self.video_path.setMinimumHeight(32)
            self.video_path.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            video_layout.addWidget(self.video_path, 1)
            image_layout = QHBoxLayout()
            image_label = QLabel('图片文件夹:')
            image_label.setStyleSheet('\n            font-size: 15px; \n            color: #2d3436; \n            font-weight: 600; \n            min-width: 90px;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            image_layout.addWidget(image_label)
            self.image_btn = QPushButton('选择文件夹')
            self.image_btn.setFixedSize(110, 32)
            self.image_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #48c6ef, stop:1 #6f86d6);\n                color: white;\n                border: none;\n                border-radius: 16px;\n                font-size: 13px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #38bdf8, stop:1 #6366f1);\n            }\n        ')
            self.image_btn.clicked.connect(self.select_image_folder)
            image_layout.addWidget(self.image_btn)
            self.image_path = QLabel('尚未选择图片文件夹')
            self.image_path.setStyleSheet('\n            border: 1px solid #e9ecef;\n            border-radius: 6px;\n            padding: 8px 12px;\n            font-size: 13px;\n            color: #6c757d;\n            background: #f8f9fa;\n        ')
            self.image_path.setMinimumHeight(32)
            self.image_path.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            image_layout.addWidget(self.image_path, 1)
            file_layout.addLayout(video_layout)
            file_layout.addLayout(image_layout)
            layout.addWidget(file_frame)
            progress_frame = QFrame()
            progress_frame.setFixedHeight(40)
            progress_frame.setStyleSheet('\n            QFrame {\n                background: white;\n                border-radius: 10px;\n                border: 1px solid #e9ecef;\n            }\n        ')
            progress_layout = QVBoxLayout(progress_frame)
            progress_layout.setContentsMargins(20, 10, 20, 10)
            self.progress_bar = QProgressBar()
            self.progress_bar.setStyleSheet('\n            QProgressBar {\n                height: 16px;\n                border-radius: 8px;\n                background: #f1f3f4;\n                text-align: center;\n                font-size: 12px;\n                font-weight: bold;\n                color: white;\n            }\n            QProgressBar::chunk {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #667eea, stop:1 #764ba2);\n                border-radius: 8px;\n            }\n        ')
            self.progress_bar.setAlignment(Qt.AlignCenter)
            progress_layout.addWidget(self.progress_bar)
            layout.addWidget(progress_frame)
            bottom_frame = QFrame()
            bottom_frame.setFixedHeight(170)
            bottom_frame.setStyleSheet('\n            QFrame {\n                background: white;\n                border-radius: 10px;\n                border: 1px solid #e9ecef;\n            }\n        ')
            bottom_layout = QHBoxLayout(bottom_frame)
            bottom_layout.setContentsMargins(20, 15, 20, 15)
            bottom_layout.setSpacing(15)
            log_section = QFrame()
            log_layout = QVBoxLayout(log_section)
            log_layout.setContentsMargins(0, 0, 0, 0)
            log_layout.setSpacing(8)
            log_label = QLabel('处理日志:')
            log_label.setStyleSheet('\n            font-size: 16px; \n            color: #2d3436; \n            font-weight: 600;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            log_layout.addWidget(log_label)
            self.log_output = QTextEdit()
            self.log_output.setReadOnly(True)
            self.log_output.setStyleSheet('\n            QTextEdit {\n                border: 2px solid #e0e6ed;\n                border-radius: 8px;\n                padding: 10px;\n                font-size: 13px;\n                color: #2d3436;\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #ffffff, stop:1 #f8f9fa);\n                font-family: \'Segoe UI\', \'Consolas\', monospace;\n                line-height: 1.6;\n            }\n            QTextEdit:focus {\n                border: 2px solid #667eea;\n            }\n            QScrollBar:vertical {\n                background: #f8f9fa;\n                width: 10px;\n                border-radius: 5px;\n            }\n            QScrollBar::handle:vertical {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #667eea, stop:1 #764ba2);\n                border-radius: 5px;\n                min-height: 20px;\n            }\n        ')
            self.log_output.setFixedHeight(115)
            log_layout.addWidget(self.log_output)
            btn_section = QFrame()
            btn_section.setFixedWidth(140)
            btn_layout = QVBoxLayout(btn_section)
            btn_layout.setContentsMargins(0, 0, 0, 0)
            btn_layout.setSpacing(8)
            btn_label = QLabel('操作控制:')
            btn_label.setStyleSheet('\n            font-size: 16px; \n            color: #2d3436; \n            font-weight: 600;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            btn_layout.addWidget(btn_label)
            self.start_btn = QPushButton('🚀 开始处理')
            self.start_btn.setFixedHeight(42)
            self.start_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #667eea, stop:1 #764ba2);\n                color: white;\n                border: none;\n                border-radius: 21px;\n                font-size: 14px;\n                font-weight: bold;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #5a67d8, stop:1 #6b46c1);\n            }\n        ')
            self.start_btn.clicked.connect(self.start_processing)
            btn_row = QHBoxLayout()
            btn_row.setSpacing(8)
            self.stop_btn = QPushButton('停止')
            self.stop_btn.setFixedSize(65, 32)
            self.stop_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #ee5a6f, stop:1 #f093fb);\n                color: white;\n                border: none;\n                border-radius: 16px;\n                font-size: 12px;\n                font-weight: 600;\n                font-family: \'Segoe UI\', sans-serif;\n                letter-spacing: 0.5px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #dc3545, stop:1 #e084eb);\n                transform: translateY(-1px);\n                box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);\n            }\n            QPushButton:pressed {\n                transform: translateY(0px);\n            }\n            QPushButton:disabled {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #dee2e6, stop:1 #ced4da);\n                color: #6c757d;\n            }\n        ')
            self.stop_btn.setEnabled(False)
            self.stop_btn.clicked.connect(self.stop_processing)
            self.clear_btn = QPushButton('清空')
            self.clear_btn.setFixedSize(65, 32)
            self.clear_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #667eea, stop:1 #764ba2);\n                color: white;\n                border: none;\n                border-radius: 16px;\n                font-size: 12px;\n                font-weight: 600;\n                font-family: \'Segoe UI\', sans-serif;\n                letter-spacing: 0.5px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #5a67d8, stop:1 #6b46c1);\n                transform: translateY(-1px);\n                box-shadow: 0 4px 10px rgba(102, 126, 234, 0.3);\n            }\n            QPushButton:pressed {\n                transform: translateY(0px);\n            }\n        ')
            self.clear_btn.clicked.connect(self.clear_log)
            btn_row.addWidget(self.stop_btn)
            btn_row.addWidget(self.clear_btn)
            btn_layout.addWidget(self.start_btn)
            btn_layout.addLayout(btn_row)
            btn_layout.addStretch()
            bottom_layout.addWidget(log_section, 1)
            bottom_layout.addWidget(btn_section)
            layout.addWidget(bottom_frame)
            layout.addStretch()
            self.processor = VideoProcessor()
            self.processor.progress_signal.connect(self.update_progress)
            self.processor.finished_signal.connect(self.processing_finished)
            self.processor.task_finished_signal.connect(self.task_finished)
            self.on_mode_changed(self.mode_combo.currentText())
            self.log_output.append('🎬 欢迎使用AI视频处理器 Pro！')
            self.log_output.append('💎 高质量模式：120fps专业级输出')
            self.log_output.append('🚀 支持智能加速，高质量视频处理')
            self.log_output.append('🔧 如硬件加速失败，可点击\'诊断\'按钮获取详细信息')
            self.log_output.append('📋 GPU处理问题排查：')
            self.log_output.append('   🟢 NVIDIA显卡：')
            self.log_output.append('     - 确保驱动版本 ≥ 417.35')
            self.log_output.append('     - 支持GTX700+或RTX系列显卡')
            self.log_output.append('   🔴 AMD显卡：')
            self.log_output.append('     - 确保驱动版本支持AMF编码')
            self.log_output.append('     - 支持RX400+或更新系列显卡')
            self.log_output.append('   ⚙️ 通用建议：')
            self.log_output.append('     - 关闭其他占用GPU的程序')
            self.log_output.append('     - 如GPU失败会自动切换到CPU模式')
            self.log_output.append('🟢 GPU绿屏问题解决方案：')
            self.log_output.append('   - 已优化色彩空间参数，减少绿屏概率')
            self.log_output.append('   - 如仍出现绿屏，请更新显卡驱动或使用CPU模式')
            self.log_output.append('   - 某些老旧显卡可能存在兼容性问题')
            self.log_output.append('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')

        def on_mode_changed(self, mode_text):
            """模式变更时更新信息"""
            if mode_text == '高质量模式优化版':
                self.processor.set_processing_mode('high_quality')
                self.mode_info.setText('💎 高质量模式：120fps，处理时间较长')
                self.log_output.append('🎭 切换到高质量模式：120fps输出')

        def on_gpu_changed(self, state):
            """NVIDIA GPU加速选项变更时的处理"""
            use_gpu = state == Qt.Checked
            if use_gpu:
                self.amd_checkbox.setChecked(False)
                self.use_gpu = True
                self.gpu_type = 'nvidia'
                self.processor.set_gpu_acceleration(True)
                self.processor.gpu_type = 'nvidia'
                self.gpu_info.setText('🚀 NVIDIA加速已启用')
                self.log_output.append('🚀 已启用NVIDIA显卡加速')
            else:
                self.use_gpu = False
                self.gpu_type = 'none'
                self.processor.set_gpu_acceleration(False)
                self.processor.gpu_type = 'none'
                self.gpu_info.setText('💻 不选择显卡默认CPU标准模式')
                self.log_output.append('💻 使用标准处理模式')

        def on_amd_changed(self, state):
            """AMD GPU加速选项变更时的处理"""
            use_amd = state == Qt.Checked
            if use_amd:
                self.gpu_checkbox.setChecked(False)
                self.use_gpu = True
                self.gpu_type = 'amd'
                self.processor.set_gpu_acceleration(True)
                self.processor.gpu_type = 'amd'
                self.amd_info.setText('🚀 AMD加速已启用')
                self.log_output.append('🚀 已启用AMD显卡加速')
            else:
                self.use_gpu = False
                self.gpu_type = 'none'
                self.processor.set_gpu_acceleration(False)
                self.processor.gpu_type = 'none'
                self.amd_info.setText('💻 标准模式')
                self.log_output.append('💻 使用标准处理模式')

        def test_gpu_support(self):
            """测试GPU支持情况"""
            self.log_output.append('🔍 正在检测硬件加速支持...')
            try:
                if self.processor.check_gpu_support():
                    gpu_type_name = 'NVIDIA' if self.processor.gpu_type == 'nvidia' else 'AMD'
                    self.gpu_info.setText(f'✅ {gpu_type_name}硬件加速可用 - 高速处理模式')
                    self.log_output.append(f'✅ 硬件检测成功：{gpu_type_name}显卡支持高速处理模式')
                    QMessageBox.information(self, '🎮 硬件检测成功', f'🎉 检测到{gpu_type_name}硬件加速支持！\n\n🚀 可以启用智能加速获得更快的处理速度\n💎 支持高质量处理')
                else:
                    self.gpu_info.setText('❌ 硬件加速不可用 - 使用标准模式')
                    self.log_output.append('❌ 硬件检测失败：使用标准处理模式')
                    QMessageBox.warning(self, '⚠️ 硬件检测失败', '❌ 未检测到NVIDIA/AMD硬件加速支持\n\n将使用标准处理模式\n🔸 兼容性更好\n🔸 处理速度较慢\n\n💡 如需详细信息，请点击\'诊断\'按钮')
            except Exception as e:
                self.gpu_info.setText('❌ 硬件检测失败')
                self.log_output.append('❌ 硬件检测出错')
                QMessageBox.critical(self, '🚨 硬件检测错误', '🚨 硬件检测过程中出现错误\n\n将使用标准处理模式')

        def detailed_gpu_diagnosis(self):
            """详细GPU诊断"""
            self.log_output.append('🔧 正在进行详细硬件诊断...')
            try:
                diag_info = self.processor.get_detailed_gpu_info()
                if 'error' in diag_info:
                    QMessageBox.critical(self, '🚨 诊断失败', f"🚨 无法完成硬件诊断\n\n原因：{diag_info['error']}")
                    return
                nvenc_available = diag_info.get('nvenc_available', False)
                nvenc_working = diag_info.get('nvenc_working', False)
                amf_available = diag_info.get('amf_available', False)
                amf_working = diag_info.get('amf_working', False)
                version_info = diag_info.get('version', '')
                report = '🔍 硬件加速诊断报告\n\n'
                if 'ffmpeg version' in version_info.lower():
                    report += '✅ FFmpeg 可用\n'
                else:
                    report += '❌ FFmpeg 异常\n'
                if nvenc_available:
                    report += '✅ NVIDIA NVENC 编码器已安装\n'
                    if nvenc_working:
                        report += '✅ NVIDIA NVENC 功能正常\n'
                        report += '✅ NVIDIA 色彩处理能力正常\n'
                    else:
                        report += '❌ NVIDIA NVENC 功能异常\n'
                        report += '   可能原因：\n'
                        report += '   🔸 NVIDIA驱动版本过旧 (需要417.35+)\n'
                        report += '   🔸 显卡型号不支持 (GTX600以下)\n'
                        report += '   🔸 系统权限不足或GPU被占用\n'
                else:
                    report += '❌ NVIDIA NVENC 编码器缺失\n'
                if amf_available:
                    report += '✅ AMD AMF 编码器已安装\n'
                    if amf_working:
                        report += '✅ AMD AMF 功能正常\n'
                        report += '✅ AMD 色彩处理能力正常\n'
                    else:
                        report += '❌ AMD AMF 功能异常\n'
                        report += '   可能原因：\n'
                        report += '   🔸 AMD驱动版本过旧\n'
                        report += '   🔸 显卡型号不支持AMF\n'
                        report += '   🔸 系统权限不足或GPU被占用\n'
                else:
                    report += '❌ AMD AMF 编码器缺失\n'
                if nvenc_working or amf_working:
                    report += '\n🚀 可以使用硬件加速！\n'
                    if nvenc_working:
                        report += '   • NVIDIA GPU加速可用\n'
                    if amf_working:
                        report += '   • AMD GPU加速可用\n'
                else:
                    report += '\n💻 建议使用CPU模式\n'
                report += '\n🟢 关于GPU色彩问题：\n'
                report += '• 已优化：强制yuv420p像素格式\n'
                report += '• 已优化：设置标准bt709色彩空间\n'
                report += '• 已优化：分离硬件解码和复杂滤镜\n'
                if nvenc_available and (not nvenc_working) or (amf_available and (not amf_working)):
                    report += '• ⚠️ 警告：当前配置可能存在色彩风险\n'
                    report += '• 💡 建议：更新显卡驱动或使用CPU模式\n'
                if not nvenc_working and (not amf_working):
                    report += '\n💡 解决建议：\n'
                    if not nvenc_available and (not amf_available):
                        report += '1. 更换支持硬件加速的FFmpeg版本\n'
                        report += '2. 或继续使用标准处理模式\n'
                    else:
                        report += '1. 更新显卡驱动至最新版本\n'
                        report += '2. 重启电脑后重试\n'
                        report += '3. 关闭其他使用GPU的程序\n'
                        if nvenc_available:
                            report += '4. NVIDIA: 检查显卡是否为GTX700+系列\n'
                        if amf_available:
                            report += '4. AMD: 检查显卡是否支持AMF编码\n'
                        report += '5. 如仍有问题，请使用CPU模式\n'
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle('🔧 硬件诊断报告')
                msg_box.setText(report)
                msg_box.setIcon(QMessageBox.Information if nvenc_working or amf_working else QMessageBox.Warning)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec_()
                self.log_output.append('📋 硬件诊断已完成，请查看诊断报告')
            except Exception as e:
                self.log_output.append('❌ 诊断过程中出现错误')
                QMessageBox.critical(self, '🚨 诊断错误', '🚨 诊断过程中出现异常\n\n请联系技术支持')

        def test_amd_support(self):
            """测试AMD GPU支持情况"""
            self.log_output.append('🔍 正在检测AMD硬件加速支持...')
            try:
                original_gpu_type = self.processor.gpu_type
                self.processor.gpu_type = 'amd'
                if self.processor._test_amf_encoding():
                    self.amd_info.setText('✅ AMD硬件加速可用 - 高速处理模式')
                    self.log_output.append('✅ AMD硬件检测成功：AMD显卡支持高速处理模式')
                    QMessageBox.information(self, '🎮 AMD硬件检测成功', '🎉 检测到AMD硬件加速支持！\n\n🚀 可以启用AMD加速获得更快的处理速度\n💎 支持高质量处理')
                else:
                    self.amd_info.setText('❌ AMD硬件加速不可用 - 使用标准模式')
                    self.log_output.append('❌ AMD硬件检测失败：使用标准处理模式')
                    QMessageBox.warning(self, '⚠️ AMD硬件检测失败', '❌ 未检测到AMD硬件加速支持\n\n将使用标准处理模式\n🔸 兼容性更好\n🔸 处理速度较慢\n\n💡 如需详细信息，请点击\'诊断\'按钮')
                self.processor.gpu_type = original_gpu_type
            except Exception as e:
                self.amd_info.setText('❌ AMD硬件检测失败')
                self.log_output.append('❌ AMD硬件检测出错')
                QMessageBox.critical(self, '🚨 AMD硬件检测错误', '🚨 AMD硬件检测过程中出现错误\n\n将使用标准处理模式')
                self.processor.gpu_type = original_gpu_type

        def detailed_amd_diagnosis(self):
            """详细AMD诊断"""
            self.log_output.append('🔧 正在进行详细AMD硬件诊断...')
            try:
                diag_info = self.processor.get_detailed_gpu_info()
                if 'error' in diag_info:
                    QMessageBox.critical(self, '🚨 AMD诊断失败', f"🚨 无法完成AMD硬件诊断\n\n原因：{diag_info['error']}")
                    return
                amf_available = diag_info.get('amf_available', False)
                amf_working = diag_info.get('amf_working', False)
                version_info = diag_info.get('version', '')
                report = '🔍 AMD硬件加速诊断报告\n\n'
                if 'ffmpeg version' in version_info.lower():
                    report += '✅ FFmpeg 可用\n'
                else:
                    report += '❌ FFmpeg 异常\n'
                if amf_available:
                    report += '✅ AMD AMF 编码器已安装\n'
                    if amf_working:
                        report += '✅ AMD AMF 功能正常\n'
                        report += '✅ AMD 色彩处理能力正常\n'
                        report += '🚀 可以使用AMD硬件加速！\n'
                    else:
                        report += '❌ AMD AMF 功能异常\n'
                        report += '   可能原因：\n'
                        report += '   🔸 AMD驱动版本过旧\n'
                        report += '   🔸 显卡型号不支持AMF (需要RX400+系列)\n'
                        report += '   🔸 系统权限不足或GPU被占用\n'
                        report += '   🔸 Windows版本过旧 (需要Windows 10+)\n'
                else:
                    report += '❌ AMD AMF 编码器缺失\n'
                    report += '   可能原因：\n'
                    report += '   🔸 FFmpeg版本不支持AMF\n'
                    report += '   🔸 AMD驱动未正确安装\n'
                    report += '   🔸 显卡型号过旧\n'
                report += '\n🔴 关于AMD GPU色彩问题：\n'
                report += '• 已优化：强制yuv420p像素格式\n'
                report += '• 已优化：设置标准bt709色彩空间\n'
                report += '• 已优化：使用CQP质量控制模式\n'
                if amf_available and (not amf_working):
                    report += '• ⚠️ 警告：当前AMD配置可能存在编码风险\n'
                    report += '• 💡 建议：更新AMD驱动或使用CPU模式\n'
                if not amf_working:
                    report += '\n💡 AMD解决建议：\n'
                    if not amf_available:
                        report += '1. 更新AMD显卡驱动至最新版本\n'
                        report += '2. 确认显卡型号支持AMF (RX400+系列)\n'
                        report += '3. 检查Windows版本 (需要Windows 10+)\n'
                        report += '4. 或继续使用标准处理模式\n'
                    else:
                        report += '1. 更新AMD驱动至最新版本\n'
                        report += '2. 重启电脑后重试\n'
                        report += '3. 关闭其他使用GPU的程序\n'
                        report += '4. 检查AMD设置中是否启用硬件加速\n'
                        report += '5. 如仍有问题，请使用CPU模式\n'
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle('🔧 AMD硬件诊断报告')
                msg_box.setText(report)
                msg_box.setIcon(QMessageBox.Information if amf_working else QMessageBox.Warning)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.exec_()
                self.log_output.append('📋 AMD硬件诊断已完成，请查看诊断报告')
            except Exception as e:
                self.log_output.append('❌ AMD诊断过程中出现错误')
                QMessageBox.critical(self, '🚨 AMD诊断错误', '🚨 AMD诊断过程中出现异常\n\n请联系技术支持')

        def select_video_folder(self):
            folder_path = QFileDialog.getExistingDirectory(self, '🎬 选择视频文件夹')
            if folder_path:
                folder_path = self._ensure_unicode_path(folder_path)
                self.processor.video_folder = folder_path
                self.video_path.setText(f'📹 {folder_path}')
                self.video_path.setStyleSheet('\n                border: 2px solid #667eea;\n                border-radius: 6px;\n                padding: 8px 12px;\n                font-size: 12px;\n                color: #2c3e50;\n                background: #e8f2ff;\n                font-weight: 500;\n            ')
                self.log_output.append(f'✅ 已选择视频文件夹: {os.path.basename(folder_path)}')

        def select_image_folder(self):
            folder_path = QFileDialog.getExistingDirectory(self, '🖼️ 选择图片文件夹')
            if folder_path:
                folder_path = self._ensure_unicode_path(folder_path)
                self.processor.image_folder = folder_path
                self.image_path.setText(f'🎨 {folder_path}')
                self.image_path.setStyleSheet('\n                border: 2px solid #48c6ef;\n                border-radius: 6px;\n                padding: 8px 12px;\n                font-size: 12px;\n                color: #2c3e50;\n                background: #e8f8ff;\n                font-weight: 500;\n            ')
                self.log_output.append(f'✅ 已选择图片文件夹: {os.path.basename(folder_path)}')

        def _ensure_unicode_path(self, path):
            """确保路径为Unicode编码，支持中文和特殊符号"""
            if isinstance(path, bytes):
                return path.decode('utf-8', errors='replace')
            return str(path)

        def start_processing(self):
            """开始处理视频和图片"""
            if not self.processor.video_folder:
                QMessageBox.warning(self, '⚠️ 提示', '🎬 请先选择视频文件夹！')
                return
            if not self.processor.image_folder:
                QMessageBox.warning(self, '⚠️ 提示', '🖼️ 请先选择图片文件夹！')
                return
            try:
                videos = self.processor._get_timed_files(self.processor.video_folder, is_video=True)
                images = self.processor._get_timed_files(self.processor.image_folder, is_video=False)
                if not videos:
                    QMessageBox.warning(self, '⚠️ 警告', '🎬 视频文件夹中无有效视频文件！')
                    return
                if not images:
                    QMessageBox.warning(self, '⚠️ 警告', '🖼️ 图片文件夹中无有效图片文件！')
                    return
            except Exception as e:
                pass
            else:
                videos.sort(key=lambda x: x['mtime'])
                images.sort(key=lambda x: x['mtime'])
                min_count = min(len(videos), len(images))
                preview = []
                for i in range(min_count):
                    v = videos[i]
                    img = images[i]
                    time_diff = abs(v['mtime'] - img['mtime'])
                    preview.append(f"  {i + 1}. 🎬 {os.path.basename(v['name'])} ↔ 🖼️ {os.path.basename(img['name'])} (时间差: {time_diff:.1f}秒)")
                preview_text = '\n'.join(preview[:10])
                if min_count > 10:
                    preview_text += f'\n  ... 共 {min_count} 对匹配文件'
                processing_status = '🚀 智能加速' if self.processor.use_gpu else '💻 标准处理'
                reply = QMessageBox.question(self, '🚀 确认处理 - 高质量模式', f'📊 即将按修改时间处理 {min_count} 对文件\n\n⚙️ 处理模式: 高质量模式（120fps）\n🔧 处理方式: {processing_status}\n\n📋 匹配预览:\n{preview_text}\n\n📁 结果将保存到output文件夹', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    self.log_output.clear()
                    self.log_output.append(f'🚀 找到 {min_count} 对按时间匹配的文件，开始处理...')
                    self.progress_bar.setValue(0)
                    self.start_btn.setEnabled(False)
                    self.stop_btn.setEnabled(True)
                    self.processor.start()
                    QMessageBox.critical(self, '🚨 错误', f'🚨 准备处理时出错: {str(e)}')
                    self.log_output.append(f'❌ 错误: {str(e)}')
                    return

        def stop_processing(self):
            """停止处理"""
            reply = QMessageBox.question(self, '⏹️ 确认停止', '⚠️ 确定要停止处理吗？\n\n📌 当前任务可能会中断', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.log_output.append('⏹️ 正在停止处理...')
                self.processor.stop()

        def update_progress(self, value, message):
            """更新进度条和日志"""
            self.progress_bar.setValue(value)
            self.progress_bar.setFormat(f'⚡ {message}')
            self.log_output.append(f'📊 {message}')

        def task_finished(self, task_name):
            """单个任务完成"""
            self.log_output.append(f'✅ 任务完成: {task_name}')

        def processing_finished(self, success, message):
            """所有处理完成"""
            self.log_output.append(f'🎉 {message}')
            self.progress_bar.setValue(100 if success else 0)
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            if hasattr(self, 'processor') and self.processor:
                if hasattr(self.processor, 'use_gpu'):
                    self.use_gpu = self.processor.use_gpu
                if hasattr(self.processor, 'gpu_available'):
                    self.gpu_available = self.processor.gpu_available
                if hasattr(self.processor, 'gpu_type'):
                    self.gpu_type = self.processor.gpu_type
                if hasattr(self, 'gpu_checkbox'):
                    self.gpu_checkbox.setChecked(self.use_gpu and self.gpu_available and (self.gpu_type == 'nvidia'))
                if hasattr(self, 'amd_checkbox'):
                    self.amd_checkbox.setChecked(self.use_gpu and self.gpu_available and (self.gpu_type == 'amd'))
            if success:
                QMessageBox.information(self, '🎉 处理完成', f'🎊 所有匹配任务处理完成！\n\n📈 共生成 {self.processor.completed_tasks} 个文件\n📁 文件保存在output文件夹')
            else:
                QMessageBox.warning(self, '⚠️ 处理中断', f'⚠️ {message}')

        def clear_log(self):
            """清空日志"""
            self.log_output.clear()
            self.log_output.append('🧹 日志已清空')

class Function2Window(QMainWindow):
        """功能二：低配电脑专用版本"""

        def __init__(self):
            super().__init__()
            self.setWindowTitle('功能二————————摩天轮笔记本电脑专用版本（低配低端电脑）')
            self.setGeometry(60, 60, 582, 540)
            self.setFixedSize(582, 540)
            set_luffy_icon(self)
            self.setStyleSheet('\n            QMainWindow {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #f5f7fa, stop:1 #c3cfe2);\n            }\n        ')
            central_widget = QWidget()
            central_widget.setStyleSheet('background: transparent;')
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(16)
            mode_info_frame = QFrame()
            mode_info_frame.setStyleSheet('\n            QFrame {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 rgba(0, 122, 255, 0.08), stop:1 rgba(88, 86, 214, 0.08));\n                border-radius: 14px;\n                border: 1px solid rgba(0, 122, 255, 0.15);\n                margin: 2px;\n            }\n        ')
            mode_info_frame.setFixedHeight(55)
            mode_info_layout = QHBoxLayout(mode_info_frame)
            mode_info_layout.setContentsMargins(20, 12, 20, 12)
            mode_icon = QLabel('🎬')
            mode_icon.setStyleSheet('\n            font-size: 18px;\n            margin-right: 8px;\n        ')
            mode_info_label = QLabel('功能二低配电脑专用：120fps优化处理，输出质量更佳')
            mode_info_label.setStyleSheet('\n            color: #007aff;\n            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n            font-size: 14px;\n            font-weight: 500;\n            letter-spacing: -0.2px;\n        ')
            mode_info_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            mode_info_layout.addWidget(mode_icon)
            mode_info_layout.addWidget(mode_info_label)
            mode_info_layout.addStretch()
            layout.addWidget(mode_info_frame)
            folder_frame = QFrame()
            folder_frame.setStyleSheet('\n            QFrame {\n                background: rgba(255, 255, 255, 0.9);\n                border-radius: 16px;\n                border: 1px solid rgba(255, 255, 255, 0.3);\n                margin: 2px;\n            }\n        ')
            folder_layout = QVBoxLayout(folder_frame)
            folder_layout.setContentsMargins(20, 18, 20, 18)
            folder_layout.setSpacing(14)
            video_row = QHBoxLayout()
            video_row.setSpacing(12)
            self.video_btn = QPushButton('📁 选择视频文件夹')
            self.video_btn.setFixedSize(140, 38)
            self.video_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #007aff, stop:1 #5856d6);\n                color: white;\n                border: none;\n                border-radius: 12px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 13px;\n                font-weight: 600;\n                letter-spacing: -0.2px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #0056cc, stop:1 #4a47b8);\n            }\n            QPushButton:pressed {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #004299, stop:1 #3d3a9a);\n            }\n        ')
            self.video_btn.clicked.connect(self.select_video_folder)
            self.video_path = QLabel('未选择视频文件夹')
            self.video_path.setStyleSheet('\n            background: rgba(246, 246, 246, 0.8);\n            border: 1px solid rgba(0, 0, 0, 0.1);\n            border-radius: 10px;\n            padding: 10px 14px;\n            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n            font-size: 13px;\n            color: #666;\n        ')
            self.video_path.setMinimumHeight(38)
            self.video_path.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            video_row.addWidget(self.video_btn)
            video_row.addWidget(self.video_path, 1)
            image_row = QHBoxLayout()
            image_row.setSpacing(12)
            self.image_btn = QPushButton('🖼️ 选择图片文件夹')
            self.image_btn.setFixedSize(140, 38)
            self.image_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #34c759, stop:1 #30d158);\n                color: white;\n                border: none;\n                border-radius: 12px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 13px;\n                font-weight: 600;\n                letter-spacing: -0.2px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #28a745, stop:1 #28c940);\n            }\n            QPushButton:pressed {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #1e7e34, stop:1 #20a132);\n            }\n        ')
            self.image_btn.clicked.connect(self.select_image_folder)
            self.image_path = QLabel('未选择图片文件夹')
            self.image_path.setStyleSheet(self.video_path.styleSheet())
            self.image_path.setMinimumHeight(38)
            self.image_path.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            image_row.addWidget(self.image_btn)
            image_row.addWidget(self.image_path, 1)
            folder_layout.addLayout(video_row)
            folder_layout.addLayout(image_row)
            layout.addWidget(folder_frame)
            progress_container = QFrame()
            progress_container.setStyleSheet('\n            QFrame {\n                background: rgba(255, 255, 255, 0.85);\n                border-radius: 14px;\n                border: 1px solid rgba(255, 255, 255, 0.3);\n                margin: 2px;\n            }\n        ')
            progress_container.setFixedHeight(70)
            progress_layout = QVBoxLayout(progress_container)
            progress_layout.setContentsMargins(20, 18, 20, 18)
            progress_layout.setAlignment(Qt.AlignCenter)
            progress_label = QLabel('处理进度')
            progress_label.setStyleSheet('\n            color: #1d1d1f;\n            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n            font-size: 14px;\n            font-weight: 600;\n            margin: 0;\n            padding: 8px 10px;\n            min-height: 20px;\n        ')
            progress_label.setAlignment(Qt.AlignCenter)
            progress_label.setWordWrap(True)
            self.progress_bar = QProgressBar()
            self.progress_bar.setFixedHeight(15)
            self.progress_bar.setStyleSheet('\n            QProgressBar {\n                background: rgba(0, 0, 0, 0.1);\n                border-radius: 2px;\n                border: none;\n            }\n            QProgressBar::chunk {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #007aff, stop:1 #5856d6);\n                border-radius: 3px;\n            }\n        ')
            self.progress_bar.setTextVisible(False)
            progress_layout.addWidget(progress_label)
            progress_layout.addSpacing(6)
            progress_layout.addWidget(self.progress_bar)
            layout.addWidget(progress_container)
            log_frame = QFrame()
            log_frame.setStyleSheet('\n            QFrame {\n                background: rgba(255, 255, 255, 0.9);\n                border-radius: 16px;\n                border: 1px solid rgba(255, 255, 255, 0.3);\n                margin: 2px;\n            }\n        ')
            log_layout = QVBoxLayout(log_frame)
            log_layout.setContentsMargins(20, 15, 20, 15)
            log_layout.setSpacing(10)
            log_header = QHBoxLayout()
            log_icon = QLabel('📝')
            log_icon.setStyleSheet('font-size: 16px;')
            log_title = QLabel('处理日志')
            log_title.setStyleSheet('\n            color: #1d1d1f;\n            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n            font-size: 15px;\n            font-weight: 600;\n            letter-spacing: -0.3px;\n        ')
            log_header.addWidget(log_icon)
            log_header.addWidget(log_title)
            log_header.addStretch()
            self.log_output = QTextEdit()
            self.log_output.setReadOnly(True)
            self.log_output.setStyleSheet('\n            QTextEdit {\n                background: rgba(248, 248, 248, 0.9);\n                border: 1px solid rgba(0, 0, 0, 0.08);\n                border-radius: 12px;\n                padding: 12px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n            font-size: 12px;\n                color: #666;\n                line-height: 1.4;\n            }\n            QScrollBar:vertical {\n                background: rgba(0, 0, 0, 0.05);\n                border: none;\n                border-radius: 4px;\n                width: 8px;\n            }\n            QScrollBar::handle:vertical {\n                background: rgba(0, 0, 0, 0.2);\n                border-radius: 4px;\n                min-height: 20px;\n            }\n            QScrollBar::handle:vertical:hover {\n                background: rgba(0, 0, 0, 0.3);\n            }\n        ')
            self.log_output.setMaximumHeight(95)
            log_layout.addLayout(log_header)
            log_layout.addWidget(self.log_output)
            layout.addWidget(log_frame)
            btn_frame = QFrame()
            btn_frame.setStyleSheet('\n            QFrame {\n                background: rgba(255, 255, 255, 0.9);\n                border-radius: 16px;\n                border: 1px solid rgba(255, 255, 255, 0.3);\n                margin: 2px;\n            }\n        ')
            btn_layout = QHBoxLayout(btn_frame)
            btn_layout.setContentsMargins(20, 15, 20, 15)
            btn_layout.setSpacing(15)
            self.start_btn = QPushButton('▶️ 开始处理')
            self.start_btn.setFixedSize(110, 40)
            self.start_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #ff3b30, stop:1 #d70015);\n                color: white;\n                border: none;\n                border-radius: 14px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 14px;\n                font-weight: 600;\n                letter-spacing: -0.2px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #e6342a, stop:1 #c20012);\n            }\n            QPushButton:pressed {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #cc2b21, stop:1 #ad000f);\n            }\n        ')
            self.start_btn.clicked.connect(self.start_processing)
            self.stop_btn = QPushButton('⏹️ 停止处理')
            self.stop_btn.setFixedSize(110, 40)
            self.stop_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #ff9500, stop:1 #ff8c00);\n                color: white;\n                border: none;\n                border-radius: 14px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 14px;\n                font-weight: 600;\n                letter-spacing: -0.2px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #e6850a, stop:1 #e67e00);\n            }\n            QPushButton:pressed {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #cc7309, stop:1 #cc7000);\n            }\n            QPushButton:disabled {\n                background: rgba(0, 0, 0, 0.1);\n                color: rgba(0, 0, 0, 0.3);\n            }\n        ')
            self.stop_btn.setEnabled(False)
            self.stop_btn.clicked.connect(self.stop_processing)
            self.clear_btn = QPushButton('🗑️ 清空日志')
            self.clear_btn.setFixedSize(110, 40)
            self.clear_btn.setStyleSheet('\n            QPushButton {\n                background: rgba(142, 142, 147, 0.2);\n                color: #1d1d1f;\n                border: 1px solid rgba(142, 142, 147, 0.3);\n                border-radius: 14px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 14px;\n                font-weight: 600;\n                letter-spacing: -0.2px;\n            }\n            QPushButton:hover {\n                background: rgba(142, 142, 147, 0.3);\n                border: 1px solid rgba(142, 142, 147, 0.4);\n            }\n            QPushButton:pressed {\n                background: rgba(142, 142, 147, 0.4);\n                border: 1px solid rgba(142, 142, 147, 0.5);\n            }\n        ')
            self.clear_btn.clicked.connect(self.clear_log)
            btn_layout.addWidget(self.start_btn)
            btn_layout.addWidget(self.stop_btn)
            btn_layout.addWidget(self.clear_btn)
            btn_layout.addStretch()
            layout.addWidget(btn_frame)
            self.processor = VideoProcessorV2()
            self.processor.progress_signal.connect(self.update_progress)
            self.processor.finished_signal.connect(self.processing_finished)
            self.processor.task_finished_signal.connect(self.task_finished)
            self.center_window()

        def center_window(self):
            """将窗口居中显示在屏幕上"""
            from PyQt5.QtWidgets import QDesktopWidget
            qr = self.frameGeometry()
            cp = QDesktopWidget().availableGeometry().center()
            qr.moveCenter(cp)
            self.move(qr.topLeft())

        def select_video_folder(self):
            folder_path = QFileDialog.getExistingDirectory(self, '选择视频文件夹')
            if folder_path:
                folder_path = self._ensure_unicode_path(folder_path)
                self.processor.video_folder = folder_path
                folder_name = os.path.basename(folder_path) or folder_path
                self.video_path.setText(f'📁 {folder_name}')
                self.video_path.setStyleSheet('\n                background: rgba(0, 122, 255, 0.1);\n                border: 1px solid rgba(0, 122, 255, 0.2);\n                border-radius: 10px;\n                padding: 10px 14px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 13px;\n                color: #007aff;\n                font-weight: 500;\n            ')
                self.log_output.append(f'✅ 已选择视频文件夹: {folder_path}')

        def select_image_folder(self):
            folder_path = QFileDialog.getExistingDirectory(self, '选择图片文件夹')
            if folder_path:
                folder_path = self._ensure_unicode_path(folder_path)
                self.processor.image_folder = folder_path
                folder_name = os.path.basename(folder_path) or folder_path
                self.image_path.setText(f'🖼️ {folder_name}')
                self.image_path.setStyleSheet('\n                background: rgba(52, 199, 89, 0.1);\n                border: 1px solid rgba(52, 199, 89, 0.2);\n                border-radius: 10px;\n                padding: 10px 14px;\n                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', system-ui, sans-serif;\n                font-size: 13px;\n                color: #34c759;\n                font-weight: 500;\n            ')
                self.log_output.append(f'✅ 已选择图片文件夹: {folder_path}')

        def _ensure_unicode_path(self, path):
            """确保路径为Unicode编码，支持中文和特殊符号"""
            if isinstance(path, bytes):
                return path.decode('utf-8', errors='replace')
            return str(path)

        def start_processing(self):
            """开始处理视频和图片"""
            if not self.processor.video_folder:
                QMessageBox.warning(self, '警告', '请先选择视频文件夹！')
                return
            if not self.processor.image_folder:
                QMessageBox.warning(self, '警告', '请先选择图片文件夹！')
                return
            try:
                videos = self.processor._get_timed_files(self.processor.video_folder, is_video=True)
                images = self.processor._get_timed_files(self.processor.image_folder, is_video=False)
                if not videos:
                    QMessageBox.warning(self, '警告', '视频文件夹中无有效视频文件！')
                    return
                if not images:
                    QMessageBox.warning(self, '警告', '图片文件夹中无有效图片文件！')
                    return
            except Exception as e:
                pass
            else:
                videos.sort(key=lambda x: x['mtime'])
                images.sort(key=lambda x: x['mtime'])
                min_count = min(len(videos), len(images))
                preview = []
                for i in range(min_count):
                    v = videos[i]
                    img = images[i]
                    time_diff = abs(v['mtime'] - img['mtime'])
                    preview.append(f"{i + 1}. 视频: {os.path.basename(v['name'])} ↔ 图片: {os.path.basename(img['name'])} (时间差: {time_diff:.1f}秒)")
                preview_text = '\n'.join(preview[:10])
                if min_count > 10:
                    preview_text += f'\n... 共 {min_count} 对匹配文件'
                reply = QMessageBox.question(self, '确认匹配 - 高质量模式', f'即将按修改时间处理 {min_count} 对文件\n\n匹配预览:\n{preview_text}\n\n结果将保存到output文件夹', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                if reply == QMessageBox.Yes:
                    self.log_output.clear()
                    self.log_output.append(f'找到 {min_count} 对按时间匹配的文件，开始处理...')
                    self.progress_bar.setValue(0)
                    self.start_btn.setEnabled(False)
                    self.stop_btn.setEnabled(True)
                    self.processor.start()
                    QMessageBox.critical(self, '错误', f'准备处理时出错: {str(e)}')
                    self.log_output.append(f'错误: {str(e)}')
                    return

        def stop_processing(self):
            """停止处理"""
            reply = QMessageBox.question(self, '确认', '确定要停止处理吗？当前任务可能会中断。', QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.log_output.append('正在停止处理...')
                self.processor.stop()

        def update_progress(self, value, message):
            """更新进度条和日志"""
            self.progress_bar.setValue(value)
            self.progress_bar.setFormat(f'{message}')
            self.log_output.append(message)

        def task_finished(self, task_name):
            """单个任务完成"""
            self.log_output.append(f'任务完成: {task_name}')

        def processing_finished(self, success, message):
            """所有处理完成"""
            self.log_output.append(message)
            self.progress_bar.setValue(100 if success else 0)
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            if hasattr(self, 'processor') and self.processor:
                self.processor.running = False
                if hasattr(self.processor, 'processing'):
                    self.processor.processing = False
            if success:
                QMessageBox.information(self, '完成', f'所有匹配任务处理完成！共生成 {self.processor.completed_tasks} 个文件')
            else:
                QMessageBox.warning(self, '处理中断', message)

        def clear_log(self):
            """清空日志"""
            self.log_output.clear()

class ComingSoonWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle('功能二：敬请期待')
            self.setFixedSize(600, 400)
            set_luffy_icon(self)
            self.setStyleSheet('\n            QMainWindow {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #667eea, stop:1 #764ba2);\n            }\n        ')
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            layout.setAlignment(Qt.AlignCenter)
            title = QLabel(' 功能二')
            title.setStyleSheet('\n            font-size: 48px;\n            font-weight: bold;\n            color: white;\n            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);\n            margin-bottom: 20px;\n        ')
            title.setAlignment(Qt.AlignCenter)
            subtitle = QLabel('尽请期待\nPlease look forward to it')
            subtitle.setStyleSheet('\n            font-size: 24px;\n            color: rgba(255,255,255,0.9);\n            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n            line-height: 1.5;\n        ')
            subtitle.setAlignment(Qt.AlignCenter)
            description = QLabel('✨ 更多精彩功能正在开发中...\n🔥 敬请关注后续更新！')
            description.setStyleSheet('\n            font-size: 16px;\n            color: rgba(255,255,255,0.8);\n            margin-top: 30px;\n            line-height: 1.6;\n        ')
            description.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            layout.addWidget(subtitle)
            layout.addWidget(description)

class Function3Window(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle('功能三：尽请期待')
            self.setFixedSize(650, 480)
            set_luffy_icon(self)
            self.setStyleSheet('\n            QMainWindow {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #f093fb, stop:1 #f5576c);\n            }\n        ')
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            layout.setAlignment(Qt.AlignCenter)
            title = QLabel('🎨 功能三')
            title.setStyleSheet('\n            font-size: 48px;\n            font-weight: bold;\n            color: white;\n            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);\n            margin-bottom: 20px;\n        ')
            title.setAlignment(Qt.AlignCenter)
            subtitle = QLabel('智能编辑器\nSmart Editor')
            subtitle.setStyleSheet('\n            font-size: 24px;\n            color: rgba(255,255,255,0.9);\n            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n            line-height: 1.5;\n        ')
            subtitle.setAlignment(Qt.AlignCenter)
            description = QLabel('🎬 AI智能剪辑功能开发中...\n✨ 让视频编辑更加简单高效！\n\n🔧 预计功能：\n• 自动剪辑和场景识别\n• 智能字幕生成\n• 音频增强处理\n• 批量特效应用')
            description.setStyleSheet('\n            font-size: 15px;\n            color: rgba(255,255,255,0.9);\n            margin-top: 20px;\n            line-height: 1.8;\n        ')
            description.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)
            layout.addWidget(subtitle)
            layout.addWidget(description)

class MainApplication(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle('摩天轮视频处理器')
            self.setFixedSize(700, 620)
            set_luffy_icon(self)
            self.setStyleSheet('\n            QMainWindow {\n                background: #f5f6fa;\n            }\n        ')
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(30, 30, 30, 30)
            layout.setSpacing(20)
            header_frame = QFrame()
            header_frame.setFixedHeight(120)
            header_frame.setStyleSheet('\n            QFrame {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                    stop:0 #667eea, stop:1 #764ba2);\n                border-radius: 15px;\n            }\n        ')
            header_layout = QVBoxLayout(header_frame)
            header_layout.setAlignment(Qt.AlignCenter)
            title = GradientLabel('摩天轮视频处理器')
            title.setStyleSheet('\n            font-size: 36px;\n            font-weight: 900;\n            background: transparent;\n            letter-spacing: 2px;\n        ')
            title.setAlignment(Qt.AlignCenter)
            font = QFont('Segoe UI', 28, QFont.Black)
            title.setFont(font)
            subtitle = QLabel('选择您需要的功能模块')
            subtitle.setStyleSheet('\n            font-size: 18px;\n            color: rgba(255,255,255,0.95);\n            margin-top: 10px;\n            font-weight: 500;\n            letter-spacing: 3px;\n            font-family: \'Segoe UI\', sans-serif;\n        ')
            subtitle.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title)
            header_layout.addWidget(subtitle)
            layout.addWidget(header_frame)
            buttons_frame = QFrame()
            buttons_frame.setStyleSheet('\n            QFrame {\n                background: white;\n                border-radius: 15px;\n                border: 1px solid #e9ecef;\n            }\n        ')
            buttons_layout = QVBoxLayout(buttons_frame)
            buttons_layout.setContentsMargins(40, 40, 40, 40)
            buttons_layout.setSpacing(20)
            function1_btn = QPushButton('🚀版本一:KKSS超强独家视频处理器')
            function1_btn.setFixedHeight(60)
            function1_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #667eea, stop:0.3 #764ba2, stop:0.6 #eb3b9b, stop:1 #f093fb);\n                color: white;\n                border: none;\n                border-radius: 30px;\n                font-size: 18px;\n                font-weight: 700;\n                text-align: left;\n                padding-left: 35px;\n                font-family: \'Segoe UI\', sans-serif;\n                letter-spacing: 1px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #5a67d8, stop:0.3 #6b46c1, stop:0.6 #d63384, stop:1 #e084eb);\n                transform: translateY(-3px);\n                box-shadow: 0 10px 25px rgba(102, 126, 234, 0.5);\n            }\n            QPushButton:pressed {\n                transform: translateY(-1px);\n            }\n        ')
            function1_btn.clicked.connect(self.open_function1)
            function2_btn = QPushButton(' 🎨 版本二：敬请期待')
            function2_btn.setFixedHeight(60)
            function2_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #48c6ef, stop:0.3 #6f86d6, stop:0.6 #a85cf9, stop:1 #ee9ca7);\n                color: white;\n                border: none;\n                border-radius: 30px;\n                font-size: 18px;\n                font-weight: 700;\n                text-align: left;\n                padding-left: 35px;\n                font-family: \'Segoe UI\', sans-serif;\n                letter-spacing: 1px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #38bdf8, stop:0.3 #6366f1, stop:0.6 #9333ea, stop:1 #df8c97);\n                transform: translateY(-3px);\n                box-shadow: 0 10px 25px rgba(72, 198, 239, 0.5);\n            }\n            QPushButton:pressed {\n                transform: translateY(-1px);\n            }\n        ')
            function2_btn.clicked.connect(self.open_function2)
            function3_btn = QPushButton('🎨 版本三：敬请期待')
            function3_btn.setFixedHeight(60)
            function3_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #f093fb, stop:0.3 #f5576c, stop:0.6 #ff6b6b, stop:1 #feca57);\n                color: white;\n                border: none;\n                border-radius: 30px;\n                font-size: 18px;\n                font-weight: 700;\n                text-align: left;\n                padding-left: 35px;\n                font-family: \'Segoe UI\', sans-serif;\n                letter-spacing: 1px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #e084eb, stop:0.3 #ee5a52, stop:0.6 #ff5252, stop:1 #f6b93d);\n                transform: translateY(-3px);\n                box-shadow: 0 10px 25px rgba(240, 147, 251, 0.5);\n            }\n            QPushButton:pressed {\n                transform: translateY(-1px);\n            }\n        ')
            function3_btn.clicked.connect(self.open_function3)
            buttons_layout.addWidget(function1_btn)
            buttons_layout.addWidget(function2_btn)
            buttons_layout.addWidget(function3_btn)
            gpu_detect_btn = QPushButton(' 👆点击检测电脑显卡情况跳转手动更新驱动')
            gpu_detect_btn.setFixedHeight(50)
            gpu_detect_btn.setStyleSheet('\n            QPushButton {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #4facfe, stop:0.3 #00f2fe, stop:0.6 #43e97b, stop:1 #38f9d7);\n                color: white;\n                border: none;\n                border-radius: 25px;\n                font-size: 16px;\n                font-weight: 700;\n                text-align: center;\n                font-family: \'Segoe UI\', sans-serif;\n                letter-spacing: 1px;\n            }\n            QPushButton:hover {\n                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,\n                    stop:0 #3b9ae1, stop:0.3 #00d4e1, stop:0.6 #2bcf5e, stop:1 #1ee6ba);\n                transform: translateY(-2px);\n                box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);\n            }\n            QPushButton:pressed {\n                transform: translateY(0px);\n            }\n        ')
            gpu_detect_btn.clicked.connect(self.show_gpu_detection_result)
            buttons_layout.addWidget(gpu_detect_btn)
            buttons_layout.addStretch()
            layout.addWidget(buttons_frame)
            footer1 = QLabel('📌 选择上方三个功能模块中的任意一个开始使用')
            footer1.setStyleSheet('\n            font-size: 14px;\n            color: #6c757d;\n            text-align: center;\n        ')
            footer1.setAlignment(Qt.AlignCenter)
            layout.addWidget(footer1)
            footer2 = QLabel('⚠️ 本软件只供本地学习参考 禁止一切违法活动')
            footer2.setStyleSheet('\n            font-size: 12px;\n            color: #dc3545;\n            text-align: center;\n            font-weight: bold;\n        ')
            footer2.setAlignment(Qt.AlignCenter)
            layout.addWidget(footer2)
            self.function1_window = None
            self.function2_window = None
            self.function3_window = None
            self.gpu_info = None
            self.has_nvidia_gpu = False

        def open_function1(self):
            """打开功能一窗口"""
            if self.function1_window is None:
                self.function1_window = VideoProcessorWindow()
            self.function1_window.show()
            self.function1_window.raise_()
            self.function1_window.activateWindow()

        def open_function2(self):
            """显示功能二开发中提示"""
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle('功能二：智能编辑器')
            msg_box.setIcon(QMessageBox.Information)
            set_luffy_icon(msg_box)
            title = '🎨 功能二'
            subtitle = '智能编辑器\nSmart Editor'
            description = '🎬 AI智能剪辑功能开发中...\n✨ 让视频编辑更加简单高效！\n\n🔧 预计功能：\n• 自动剪辑和场景识别\n• 智能字幕生成\n• 音频增强处理\n• 批量特效应用'
            full_message = f'{title}\n\n{subtitle}\n\n{description}'
            msg_box.setText(full_message)
            msg_box.setStyleSheet('\n            QMessageBox {\n                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,\n                    stop:0 #667eea, stop:1 #764ba2);\n                color: white;\n                font-family: \'Segoe UI\', sans-serif;\n            }\n            QMessageBox QLabel {\n                color: white;\n                font-size: 14px;\n                line-height: 1.6;\n            }\n            QMessageBox QPushButton {\n                background: rgba(255, 255, 255, 0.2);\n                color: white;\n                border: 1px solid rgba(255, 255, 255, 0.3);\n                border-radius: 6px;\n                padding: 8px 16px;\n                font-weight: bold;\n            }\n            QMessageBox QPushButton:hover {\n                background: rgba(255, 255, 255, 0.3);\n            }\n        ')
            msg_box.exec_()

        def open_function3(self):
            """打开功能三窗口"""
            if self.function3_window is None:
                self.function3_window = Function3Window()
            self.function3_window.show()
            self.function3_window.raise_()
            self.function3_window.activateWindow()

        def detect_gpu_compatibility(self):
            """检测显卡型号、驱动版本和兼容性"""
            try:
                import subprocess
                import re
                cmd = ['wmic', 'path', 'win32_VideoController', 'get', 'name,driverversion,driverdate', '/format:csv']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    output = result.stdout.strip()
                    lines = output.split('\n')
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.split(',')
                            if len(parts) >= 4:
                                gpu_name = parts[3].strip()
                                driver_version = parts[2].strip() if len(parts) > 2 else '未知'
                                driver_date = parts[1].strip() if len(parts) > 1 else '未知'
                                if self._is_discrete_gpu(gpu_name):
                                    gpu_type = self._get_gpu_type(gpu_name)
                                    compatibility = self._check_gpu_compatibility(gpu_name, driver_version)
                                    return {'gpu_detected': True, 'gpu_name': gpu_name, 'gpu_model': self._extract_gpu_model(gpu_name), 'gpu_type': gpu_type, 'driver_version': driver_version, 'driver_date': driver_date, 'is_compatible': compatibility['compatible'], 'driver_status': compatibility['driver_status'], 'driver_url': compatibility['driver_url'], 'min_driver_version': compatibility['min_version']}
                    return {'gpu_detected': False, 'gpu_name': '集成显卡或未检测到独立显卡', 'gpu_model': '未知', 'gpu_type': 'integrated', 'driver_version': '未知', 'driver_date': '未知', 'is_compatible': False, 'driver_status': 'unknown', 'driver_url': '', 'min_driver_version': ''}
                else:
                    return self._detect_gpu_compatibility_fallback()
            except Exception as e:
                print(f'显卡兼容性检测失败: {str(e)}')
                return self._detect_gpu_compatibility_fallback()

        def _is_discrete_gpu(self, gpu_name):
            """判断是否为独立显卡"""
            gpu_lower = gpu_name.lower()
            nvidia_discrete = ['geforce', 'quadro', 'tesla', 'rtx', 'gtx']
            amd_discrete = ['radeon', 'rx ', 'r9', 'r7', 'r5', 'vega', 'navi']
            integrated_keywords = ['intel', 'uhd', 'iris', 'hd graphics', 'vega 8', 'vega 11']
            for keyword in integrated_keywords:
                if keyword in gpu_lower:
                    return False
            else:
                for keyword in nvidia_discrete + amd_discrete:
                    if keyword in gpu_lower:
                        return True
                else:
                    return False

        def _get_gpu_type(self, gpu_name):
            """获取显卡类型"""
            gpu_lower = gpu_name.lower()
            if any((keyword in gpu_lower for keyword in ['nvidia', 'geforce', 'quadro', 'tesla', 'rtx', 'gtx'])):
                return 'nvidia'
            if any((keyword in gpu_lower for keyword in ['amd', 'radeon', 'rx'])):
                return 'amd'
            return 'unknown'

        def _extract_gpu_model(self, gpu_name):
            """提取显卡型号"""
            import re
            nvidia_pattern = '(GTX|RTX)\\s*(\\d+)\\s*(Ti|SUPER)?'
            nvidia_match = re.search(nvidia_pattern, gpu_name, re.IGNORECASE)
            if nvidia_match:
                series = nvidia_match.group(1).upper()
                model = nvidia_match.group(2)
                suffix = nvidia_match.group(3) or ''
                return f'{series} {model} {suffix}'.strip()
            amd_pattern = '(RX|R9|R7|R5)\\s*(\\d+)\\s*(XT|X)?'
            amd_match = re.search(amd_pattern, gpu_name, re.IGNORECASE)
            if amd_match:
                series = amd_match.group(1).upper()
                model = amd_match.group(2)
                suffix = amd_match.group(3) or ''
                return f'{series} {model} {suffix}'.strip()
            return gpu_name

        def _check_gpu_compatibility(self, gpu_name, driver_version):
            """检查显卡兼容性和驱动版本"""
            gpu_lower = gpu_name.lower()
            gpu_type = self._get_gpu_type(gpu_name)
            if gpu_type == 'nvidia':
                return self._check_nvidia_compatibility(gpu_name, driver_version)
            if gpu_type == 'amd':
                return self._check_amd_compatibility(gpu_name, driver_version)
            return {'compatible': False, 'driver_status': 'unknown', 'driver_url': '', 'min_version': ''}

        def _check_nvidia_compatibility(self, gpu_name, driver_version):
            """检查NVIDIA显卡兼容性"""
            import re
            gpu_lower = gpu_name.lower()
            compatible_series = ['gtx 6', 'gtx 7', 'gtx 8', 'gtx 9', 'gtx 10', 'gtx 16', 'rtx 20', 'rtx 30', 'rtx 40']
            is_compatible = any((series in gpu_lower for series in compatible_series))
            is_compatible = False
            min_driver_version = '417.35'
            driver_url = 'https://www.nvidia.cn/drivers/'
            if driver_version and driver_version!= '未知':
                try:
                    current_version = float(driver_version.split('.')[0] + '.' + driver_version.split('.')[1])
                    min_version = float(min_driver_version.split('.')[0] + '.' + min_driver_version.split('.')[1])
                    if current_version >= min_version:
                        driver_status = 'good'
                    else:
                        if current_version >= min_version - 50:
                            driver_status = 'old'
                        else:
                            driver_status = 'too_old'
                except:
                    driver_status = 'unknown'
            else:
                driver_status = 'unknown'
            return {'compatible': is_compatible, 'driver_status': driver_status, 'driver_url': driver_url, 'min_version': min_driver_version}

        def _check_amd_compatibility(self, gpu_name, driver_version):
            """检查AMD显卡兼容性"""
            gpu_lower = gpu_name.lower()
            compatible_series = ['rx 4', 'rx 5', 'rx 6', 'rx 7', 'vega']
            is_compatible = any((series in gpu_lower for series in compatible_series))
            driver_url = 'https://www.amd.com/zh-hans/support'
            if driver_version and driver_version!= '未知':
                driver_status = 'good'
            else:
                driver_status = 'unknown'
            return {'compatible': is_compatible, 'driver_status': driver_status, 'driver_url': driver_url, 'min_version': '最新版本'}

        def _detect_gpu_compatibility_fallback(self):
            """备用显卡兼容性检测方法"""
            try:
                gpu_info = self._detect_gpu_fallback()
                if gpu_info and gpu_info.get('nvidia_gpus'):
                    gpu_name = gpu_info['nvidia_gpus'][0]
                    compatibility = self._check_nvidia_compatibility(gpu_name, '未知')
                    return {'gpu_detected': True, 'gpu_name': gpu_name, 'gpu_model': self._extract_gpu_model(gpu_name), 'gpu_type': 'nvidia', 'driver_version': '未知', 'driver_date': '未知', 'is_compatible': compatibility['compatible'], 'driver_status': 'unknown', 'driver_url': compatibility['driver_url'], 'min_driver_version': compatibility['min_version']}
                return {'gpu_detected': False, 'gpu_name': '未检测到兼容显卡', 'gpu_model': '未知', 'gpu_type': 'unknown', 'driver_version': '未知', 'driver_date': '未知', 'is_compatible': False, 'driver_status': 'unknown', 'driver_url': '', 'min_driver_version': ''}
            except:
                return {'gpu_detected': False, 'gpu_name': '检测失败', 'gpu_model': '未知', 'gpu_type': 'unknown', 'driver_version': '未知', 'driver_date': '未知', 'is_compatible': False, 'driver_status': 'unknown', 'driver_url': '', 'min_driver_version': ''}

        def detect_gpu(self):
            """保持向后兼容的显卡检测方法"""
            try:
                import subprocess
                import re
                cmd = ['wmic', 'path', 'win32_VideoController', 'get', 'name']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    output = result.stdout.strip()
                    gpu_names = []
                    lines = output.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and line != 'Name':
                            gpu_names.append(line)

                    nvidia_gpus = []
                    for gpu in gpu_names:
                        if 'nvidia' in gpu.lower() or 'geforce' in gpu.lower() or 'quadro' in gpu.lower() or 'tesla' in gpu.lower():
                            nvidia_gpus.append(gpu)
                            self.has_nvidia_gpu = True

                    self.gpu_info = {'all_gpus': gpu_names, 'nvidia_gpus': nvidia_gpus, 'has_nvidia': self.has_nvidia_gpu}
                    return self.gpu_info
                else:
                    return self._detect_gpu_fallback()
            except Exception as e:
                print(f'GPU检测失败: {str(e)}')
                return self._detect_gpu_fallback()

        def _detect_gpu_fallback(self):
            """备用显卡检测方法"""
            try:
                import subprocess
                import os
                import time
                cmd = ['dxdiag', '/t', 'temp_gpu_info.txt']
                subprocess.run(cmd, timeout=30, creationflags=subprocess.CREATE_NO_WINDOW)
                time.sleep(2)
                if os.path.exists('temp_gpu_info.txt'):
                    with open('temp_gpu_info.txt', 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        gpu_names = []
                        nvidia_gpus = []
                        lines = content.split('\n')
                        for line in lines:
                            if 'Card name:' in line or 'Chip type:' in line:
                                gpu_name = line.split(':', 1)[1].strip()
                                if gpu_name and gpu_name not in gpu_names:
                                    gpu_names.append(gpu_name)
                                    if 'nvidia' in gpu_name.lower() or 'geforce' in gpu_name.lower():
                                        nvidia_gpus.append(gpu_name)
                                        self.has_nvidia_gpu = True

                        try:
                            os.remove('temp_gpu_info.txt')
                        except:
                            pass

                        self.gpu_info = {'all_gpus': gpu_names, 'nvidia_gpus': nvidia_gpus, 'has_nvidia': self.has_nvidia_gpu}
                        return self.gpu_info

                self.gpu_info = {'all_gpus': ['未检测到显卡信息'], 'nvidia_gpus': [], 'has_nvidia': False}
                return self.gpu_info
            except Exception as e:
                print(f'备用GPU检测失败: {str(e)}')
                self.gpu_info = {'all_gpus': ['未检测到显卡信息'], 'nvidia_gpus': [], 'has_nvidia': False}
                return self.gpu_info

        def show_gpu_detection_result(self):
            """显示显卡检测结果和兼容性分析，并自动重定向到驱动更新网站"""
            gpu_info = self.detect_gpu_compatibility()
            if gpu_info:
                should_redirect = False
                redirect_url = ''
                if gpu_info['is_compatible'] and gpu_info['driver_url']:
                    if gpu_info['driver_status'] in ['old', 'too_old']:
                        should_redirect = True
                        redirect_url = gpu_info['driver_url']
                report = '🔍 显卡兼容性检测报告\n\n'
                report += '📋 基本信息：\n'
                report += f"   显卡名称：{gpu_info['gpu_name']}\n"
                report += f"   显卡型号：{gpu_info['gpu_model']}\n"
                report += f"   显卡类型：{gpu_info['gpu_type'].upper()}\n"
                report += f"   驱动版本：{gpu_info['driver_version']}\n"
                if gpu_info['driver_date']!= '未知':
                    report += f"   驱动日期：{gpu_info['driver_date']}\n"
                report += '\n'
                report += '🔧 兼容性分析：\n'
                if gpu_info['is_compatible']:
                    report += '   ✅ 硬件加速：支持\n'
                    if gpu_info['driver_status'] == 'good':
                        report += '   ✅ 驱动状态：良好\n'
                        report += '   💡 建议：可以启用GPU加速获得最佳性能\n'
                    else:
                        if gpu_info['driver_status'] == 'old':
                            report += '   ⚠️ 驱动状态：版本较旧\n'
                            report += '   💡 建议：更新驱动以获得更好的稳定性\n'
                            if gpu_info['driver_url']:
                                report += f"   🔗 更新地址：{gpu_info['driver_url']}\n"
                                report += '   🌐 即将自动打开驱动更新网站...\n'
                        else:
                            if gpu_info['driver_status'] == 'too_old':
                                report += '   ❌ 驱动状态：版本过低\n'
                                report += '   💡 建议：必须更新驱动才能正常使用GPU加速\n'
                                if gpu_info['driver_url']:
                                    report += f"   🔗 更新地址：{gpu_info['driver_url']}\n"
                                    report += '   🌐 即将自动打开驱动更新网站...\n'
                            else:
                                report += '   ❓ 驱动状态：无法确定\n'
                                report += '   💡 建议：建议更新到最新驱动版本\n'
                                if gpu_info['driver_url']:
                                    report += f"   🔗 更新地址：{gpu_info['driver_url']}\n"
                else:
                    report += '   ❌ 硬件加速：不支持\n'
                    if gpu_info['gpu_type'] == 'nvidia':
                        report += '   �� 原因：显卡型号过旧，不支持NVENC编码\n'
                        report += '   💡 建议：使用CPU模式处理，或升级显卡\n'
                    else:
                        if gpu_info['gpu_type'] == 'amd':
                            report += '   📝 原因：显卡型号过旧，不支持AMF编码\n'
                            report += '   💡 建议：使用CPU模式处理，或升级显卡\n'
                        else:
                            report += '   📝 原因：未检测到支持的独立显卡\n'
                            report += '   💡 建议：使用CPU模式处理\n'
                report += '\n'
                report += '🚀 功能推荐：\n'
                if gpu_info['is_compatible'] and gpu_info['driver_status'] in ['good', 'old']:
                    report += '   推荐使用：版本一（AI视频处理器 Pro）\n'
                    report += '   ✅ 可启用GPU硬件加速\n'
                    report += '   ✅ 处理速度更快\n'
                    report += '   ✅ 支持高级功能\n'
                else:
                    report += '   推荐使用：版本二（低配电脑专用版）\n'
                    report += '   ✅ 兼容性更好\n'
                    report += '   ✅ 稳定性更高\n'
                    report += '   ✅ 不依赖独立显卡\n'
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle('🔍 显卡兼容性检测报告')
                msg_box.setText(report)
                msg_box.setIcon(QMessageBox.Information)
                set_luffy_icon(msg_box)
                msg_box.setStyleSheet('\n                QMessageBox {\n                    background: white;\n                    font-family: \'Segoe UI\', sans-serif;\n                }\n                QMessageBox QLabel {\n                    color: #2d3436;\n                    font-size: 13px;\n                    line-height: 1.5;\n                }\n                QMessageBox QPushButton {\n                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                        stop:0 #667eea, stop:1 #764ba2);\n                    color: white;\n                    border: none;\n                    border-radius: 6px;\n                    padding: 8px 16px;\n                    font-weight: bold;\n                    min-width: 80px;\n                }\n                QMessageBox QPushButton:hover {\n                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,\n                        stop:0 #5a67d8, stop:1 #6b46c1);\n                }\n            ')
                msg_box.exec_()
                if should_redirect and redirect_url:
                    try:
                        import webbrowser
                        gpu_brand = 'NVIDIA' if gpu_info['gpu_type'] == 'nvidia' else 'AMD'
                        redirect_msg = QMessageBox(self)
                        redirect_msg.setWindowTitle(f'🌐 {gpu_brand}驱动更新')
                        redirect_msg.setText(f'🔄 正在为您打开{gpu_brand}驱动更新网站...\n\n📥 请下载并安装最新驱动程序以获得最佳性能')
                        redirect_msg.setIcon(QMessageBox.Information)
                        set_luffy_icon(redirect_msg)
                        redirect_msg.setStandardButtons(QMessageBox.Ok)
                        redirect_msg.setStyleSheet(msg_box.styleSheet())
                        webbrowser.open(redirect_url)
                        redirect_msg.exec_()
                    except Exception as e:
                        pass
            else:
                QMessageBox.warning(self, '⚠️ 检测失败', '❌ 显卡兼容性检测失败\n\n💡 建议：\n🛡️ 使用版本二（低配电脑专用版）')
                error_msg = QMessageBox(self)
                error_msg.setWindowTitle('⚠️ 浏览器打开失败')
                error_msg.setText(f'❌ 无法自动打开浏览器\n\n🔗 请手动访问：\n{redirect_url}')
                error_msg.setIcon(QMessageBox.Warning)
                set_luffy_icon(error_msg)
                error_msg.exec_()
                return None

        def auto_detect_and_recommend(self):
            """启动时检测显卡型号、驱动和兼容性"""
            gpu_info = self.detect_gpu_compatibility()
            if gpu_info:
                report = '🔍 显卡兼容性检测报告\n\n'
                if gpu_info['gpu_detected']:
                    report += f"📋 检测到显卡：{gpu_info['gpu_name']}\n"
                    report += f"🏷️ 显卡型号：{gpu_info['gpu_model']}\n\n"
                    if gpu_info['is_compatible']:
                        report += '✅ 显卡兼容性：支持硬件加速\n'
                        if gpu_info['driver_status'] == 'good':
                            report += '✅ 驱动状态：版本良好，可正常使用\n'
                        else:
                            if gpu_info['driver_status'] == 'old':
                                report += '⚠️ 驱动状态：版本较旧，建议更新\n'
                                report += f"🔗 驱动更新地址：{gpu_info['driver_url']}\n"
                            else:
                                report += '❌ 驱动状态：版本过低，需要更新\n'
                                report += f"🔗 驱动更新地址：{gpu_info['driver_url']}\n"
                    else:
                        report += '❌ 显卡兼容性：不支持硬件加速\n'
                        report += '💡 建议：使用CPU模式处理\n'
                else:
                    report += '❌ 未检测到独立显卡\n'
                    report += '💡 建议：使用CPU模式处理\n'
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle('🔍 显卡兼容性检测')
                msg_box.setText(report)
                msg_box.setIcon(QMessageBox.Information)
                set_luffy_icon(msg_box)
                msg_box.exec_()
if __name__ == '__main__':
    try:
        import locale
        locale.setlocale(locale.LC_ALL, '')
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
    except Exception as e:
        print(f'设置系统编码失败: {str(e)}')

    try:
        from PyQt5.QtCore import Qt
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    except Exception as e:
        print(f'设置高DPI支持失败: {str(e)}')

    app = QApplication(sys.argv)

    try:
        # 尝试设置应用图标
        print('⚠️ 未找到本地图标文件，使用默认图标')
    except Exception as e:
        print(f'设置应用图标失败: {str(e)}')

    try:
        font = app.font()
        font.setFamily('Segoe UI')
        font.setPointSize(10)
        font.setHintingPreference(QFont.PreferFullHinting)
        app.setFont(font)
        app.setStyleSheet('''
            * {
                font-family: 'Segoe UI', 'Microsoft YaHei UI', 'Arial', sans-serif;
                -webkit-font-smoothing: antialiased;
            }
        ''')
    except Exception as e:
        print(f'设置字体失败: {str(e)}')

    window = MainApplication()
    window.show()
    sys.exit(app.exec_())