import os
import sys
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import queue
import shutil
import tempfile
import subprocess
from datetime import datetime

def get_ffmpeg_path():
    exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    ffmpeg_path = os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffmpeg.exe')
    if os.path.exists(ffmpeg_path):
        return ffmpeg_path
    raise FileNotFoundError(f"未找到ffmpeg: {ffmpeg_path}")

def get_ffprobe_path():
    exe_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
    ffprobe_path = os.path.join(exe_dir, 'ffmpeg', 'bin', 'ffprobe.exe')
    if os.path.exists(ffprobe_path):
        return ffprobe_path
    raise FileNotFoundError(f"未找到ffprobe: {ffprobe_path}")

class 爱快AB(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("爱快AB - 多轨合成工具")
        self.geometry("800x600")
        self.main_file = ""
        self.aux_file = ""
        self.main_dir = ""
        self.aux_dir = ""
        self.output_dir = ""
        self.log_queue = queue.Queue()
        self.batch_mode_var = tk.BooleanVar()
        self.setup_ui()
        self.after(100, self.process_log_queue)

    def setup_ui(self):
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill="both", expand=True)
        main_frame.grid_columnconfigure(1, weight=1)

        self.batch_mode_check = ttk.Checkbutton(
            main_frame,
            text="启用批量模式",
            variable=self.batch_mode_var,
            command=self.toggle_batch_mode
        )
        self.batch_mode_check.grid(row=0, column=1, pady=(0, 10), sticky='w')

        self.main_label = ttk.Label(main_frame, text="主视频：")
        self.main_label.grid(row=1, column=0, padx=(0, 5), pady=5, sticky="w")
        self.main_entry = ttk.Entry(main_frame)
        self.main_entry.grid(row=1, column=1, sticky="ew")
        self.main_browse_button = ttk.Button(main_frame, text="浏览...", command=self.browse_main_file)
        self.main_browse_button.grid(row=1, column=2, padx=5)

        self.aux_label = ttk.Label(main_frame, text="副视频：")
        self.aux_label.grid(row=2, column=0, padx=(0, 5), pady=5, sticky="w")
        self.aux_entry = ttk.Entry(main_frame)
        self.aux_entry.grid(row=2, column=1, sticky="ew")
        self.aux_browse_button = ttk.Button(main_frame, text="浏览...", command=self.browse_aux_file)
        self.aux_browse_button.grid(row=2, column=2, padx=5)

        self.out_label = ttk.Label(main_frame, text="输出文件夹：")
        self.out_label.grid(row=3, column=0, padx=(0, 5), pady=5, sticky="w")
        self.out_entry = ttk.Entry(main_frame)
        self.out_entry.grid(row=3, column=1, sticky="ew")
        self.out_browse_btn = ttk.Button(main_frame, text="浏览...", command=self.browse_output_dir)
        self.out_browse_btn.grid(row=3, column=2, padx=5)

        self.start_btn = ttk.Button(main_frame, text="开始处理", command=self.start_process)
        self.start_btn.grid(row=4, column=1, pady=10)

        log_frame = ttk.LabelFrame(main_frame, text="处理日志")
        log_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0), sticky="nsew")
        main_frame.grid_rowconfigure(5, weight=1)
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(0, weight=1)
        self.log_textbox = tk.Text(log_frame, state="disabled", wrap="word", height=15)
        self.log_textbox.grid(row=0, column=0, sticky="nsew")
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_textbox.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.log_textbox['yscrollcommand'] = scrollbar.set

        self.toggle_batch_mode()

    def toggle_batch_mode(self):
        is_batch = self.batch_mode_var.get()
        if is_batch:
            self.main_label.config(text="主视频文件夹：")
            self.aux_label.config(text="副视频文件夹：")
            self.main_browse_button.config(command=self.browse_main_dir)
            self.aux_browse_button.config(command=self.browse_aux_dir)
            self.start_btn.config(text="开始批量处理")
            self.main_entry.delete(0, tk.END)
            self.aux_entry.delete(0, tk.END)
        else:
            self.main_label.config(text="主视频：")
            self.aux_label.config(text="副视频：")
            self.main_browse_button.config(command=self.browse_main_file)
            self.aux_browse_button.config(command=self.browse_aux_file)
            self.start_btn.config(text="开始处理")
            self.main_entry.delete(0, tk.END)
            self.aux_entry.delete(0, tk.END)

    def browse_main_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4 *.mkv *.mov *.avi")])
        if file_path:
            self.main_file = file_path
            self.main_entry.delete(0, tk.END)
            self.main_entry.insert(0, file_path)

    def browse_aux_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4 *.mkv *.mov *.avi")])
        if file_path:
            self.aux_file = file_path
            self.aux_entry.delete(0, tk.END)
            self.aux_entry.insert(0, file_path)

    def browse_main_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.main_dir = dir_path
            self.main_entry.delete(0, tk.END)
            self.main_entry.insert(0, dir_path)

    def browse_aux_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.aux_dir = dir_path
            self.aux_entry.delete(0, tk.END)
            self.aux_entry.insert(0, dir_path)

    def browse_output_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.output_dir = dir_path
            self.out_entry.delete(0, tk.END)
            self.out_entry.insert(0, dir_path)

    def start_process(self):
        is_batch = self.batch_mode_var.get()
        main_path = self.main_entry.get()
        aux_path = self.aux_entry.get()
        output_dir = self.out_entry.get()
        if is_batch:
            if not main_path or not os.path.isdir(main_path):
                messagebox.showerror("错误", "请选择有效的主视频文件夹！")
                return
            if not aux_path or not os.path.isdir(aux_path):
                messagebox.showerror("错误", "请选择有效的副视频文件夹！")
                return
            if not output_dir or not os.path.isdir(output_dir):
                messagebox.showerror("错误", "请选择有效的输出目录！")
                return
            self.main_dir = main_path
            self.aux_dir = aux_path
            self.output_dir = output_dir
        else:
            if not main_path or not os.path.isfile(main_path):
                messagebox.showerror("错误", "请选择有效的主视频！")
                return
            if not aux_path or not os.path.isfile(aux_path):
                messagebox.showerror("错误", "请选择有效的副视频！")
                return
            if not output_dir or not os.path.isdir(output_dir):
                messagebox.showerror("错误", "请选择有效的输出目录！")
                return
            self.main_file = main_path
            self.aux_file = aux_path
            self.output_dir = output_dir
        self.set_ui_state("disabled")
        self.clear_log()
        threading.Thread(target=self.process_video, args=(is_batch,), daemon=True).start()

    def set_ui_state(self, state):
        widgets = [
            self.main_entry, self.aux_entry, self.out_entry,
            self.main_browse_button, self.aux_browse_button, self.out_browse_btn, self.start_btn, self.batch_mode_check
        ]
        for w in widgets:
            w.config(state=state)

    def log(self, msg):
        self.log_queue.put(msg)

    def clear_log(self):
        self.log_textbox.configure(state="normal")
        self.log_textbox.delete(1.0, tk.END)
        self.log_textbox.configure(state="disabled")

    def process_log_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                if message == "PROCESS_FINISHED":
                    self.set_ui_state("normal")
                    messagebox.showinfo("完成", "处理完成！")
                elif message == "PROCESS_FAILED":
                    self.set_ui_state("normal")
                    messagebox.showerror("失败", "处理过程中发生错误，请查看日志。")
                else:
                    self.log_textbox.configure(state="normal")
                    self.log_textbox.insert("end", message + "\n")
                    self.log_textbox.configure(state="disabled")
                    self.log_textbox.see("end")
        except queue.Empty:
            pass
        finally:
            self.after(100, self.process_log_queue)

    def _run_ffmpeg_pipeline(self, main_file, aux_file, basename):
        import os
        from datetime import datetime
        # 获取主视频分辨率和时长
        self.log("第一步处理中...")
        ffprobe_path = get_ffprobe_path()
        # 获取分辨率
        cmd_res = [ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'csv=p=0', main_file]
        res = subprocess.run(cmd_res, capture_output=True, text=True).stdout.strip()
        width, height = map(int, res.split(','))
        # 获取时长
        cmd_dur = [ffprobe_path, '-v', 'quiet', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', main_file]
        duration = float(subprocess.run(cmd_dur, capture_output=True, text=True).stdout.strip())
        duration_str = f"{duration:.2f}"
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        with tempfile.TemporaryDirectory() as temp_dir:
            # 1. 副视频静音+缩放+补时长
            self.log("第二步处理中...")
            temp_b_silent = os.path.join(temp_dir, "b_silent_scaled.mp4")
            cmd1 = [get_ffmpeg_path(), "-y", "-stream_loop", "-1", "-i", aux_file, "-t", duration_str, "-vf", f"scale={width}:{height}", "-an", "-c:v", "libx264", "-preset", "medium", temp_b_silent]
            self.run_cmd(cmd1)
            # 2. 多轨合成
            self.log("第三步处理中...")
            out_file = os.path.join(self.output_dir, f"{timestamp}_{basename}_爱快AB.mp4")
            cmd2 = [get_ffmpeg_path(), "-y", "-hide_banner", "-i", temp_b_silent, "-i", main_file,
                    "-map", "0:v:0", "-map", "1:v:0", "-map", "1:a:0",
                    "-c:v", "libx264", "-crf", "18", "-preset", "medium", "-c:a", "aac", "-b:a", "320k",
                    "-disposition:v:0", "0", "-disposition:v:1", "default", "-disposition:a:0", "default",
                    "-map_metadata", "-1", out_file]
            self.run_cmd(cmd2)
            self.log(f"第四步处理中... 输出文件：{out_file}")

    def run_cmd(self, cmd):
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore'
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{cmd}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")
        # 正常时不输出任何FFmpeg日志

    def process_video(self, is_batch):
        try:
            if is_batch:
                video_extensions = ('.mp4', '.mkv', '.mov', '.avi')
                main_list = sorted([f for f in os.listdir(self.main_dir) if f.lower().endswith(video_extensions)])
                aux_list = sorted([f for f in os.listdir(self.aux_dir) if f.lower().endswith(video_extensions)])
                if not main_list or not aux_list:
                    self.log("主/副视频文件夹中未找到视频文件！")
                    self.log_queue.put("PROCESS_FAILED")
                    return
                min_len = min(len(main_list), len(aux_list))
                for idx in range(min_len):
                    try:
                        main_file = os.path.join(self.main_dir, main_list[idx])
                        aux_file = os.path.join(self.aux_dir, aux_list[idx])
                        basename = os.path.splitext(main_list[idx])[0]
                        self.log(f"[{idx+1}/{min_len}] 主：{main_list[idx]} 副：{aux_list[idx]}")
                        self._run_ffmpeg_pipeline(main_file, aux_file, basename)
                        self.log(f"✔️ 完成：{basename}")
                    except Exception as e:
                        self.log(f"❌ 失败：{main_list[idx]} + {aux_list[idx]}，原因：{e}")
                self.log_queue.put("PROCESS_FINISHED")
            else:
                basename = os.path.splitext(os.path.basename(self.main_file))[0]
                self._run_ffmpeg_pipeline(self.main_file, self.aux_file, basename)
                self.log_queue.put("PROCESS_FINISHED")
        except Exception as e:
            self.log(f"出错：{e}")
            self.log_queue.put("PROCESS_FAILED")

# 新增：无界面可集成类
class WujiAB:
    def __init__(self, log_callback=None, ffmpeg_path=None, ffprobe_path=None):
        self.log = log_callback if log_callback else (lambda msg: None)
        self.ffmpeg_path = ffmpeg_path or get_ffmpeg_path()
        self.ffprobe_path = ffprobe_path or get_ffprobe_path()

    def get_video_duration(self, video_path):
        cmd = [self.ffprobe_path, '-v', 'quiet', '-show_entries', 'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        try:
            return float(result.stdout.strip())
        except Exception:
            return 0.0

    def get_video_resolution(self, video_path):
        cmd = [self.ffprobe_path, '-v', 'error', '-select_streams', 'v:0', '-show_entries', 'stream=width,height', '-of', 'csv=p=0', video_path]
        result = subprocess.run(cmd, capture_output=True, text=True)
        w, h = result.stdout.strip().split(',')
        return int(w), int(h)

    def run_processing_logic(self, video_a_list, video_b_list, output_dir_path, is_batch, del_a=False, del_b=False):
        total = len(video_a_list)
        for idx, (a, b) in enumerate(zip(video_a_list, video_b_list), 1):
            # 健壮性增强：跳过无效文件
            if not (os.path.isfile(a) and os.path.isfile(b)):
                self.log(f"[跳过] 第{idx}组: {a} 或 {b} 不是有效文件")
                continue
            if not os.path.splitext(a)[1].lower() in ['.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv'] or not os.path.splitext(b)[1].lower() in ['.mp4', '.mov', '.avi', '.mkv', '.flv', '.wmv']:
                self.log(f"[跳过] 第{idx}组: {a} 或 {b} 不是视频文件")
                continue
            try:
                self.process_single_video_pair(idx, total, a, b, output_dir_path, del_a=del_a, del_b=del_b)
            except Exception as e:
                self.log(f"❌ 批量处理第{idx}组失败: {e}")
                self.log("PROCESS_FAILED")
                continue
        self.log("\n🎉 全部完成")
        self.log("PROCESS_FINISHED")

    def process_single_video_pair(self, current_num, total_num, video_a_path, video_b_path, output_dir_path, del_a=False, del_b=False):
        try:
            self.log(f"\n[{current_num}/{total_num}] 主视频: {os.path.basename(video_b_path)}, 副视频: {os.path.basename(video_a_path)}")
            input_basename = os.path.basename(video_b_path)
            input_name = os.path.splitext(input_basename)[0]
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            final_output_path = os.path.join(output_dir_path, f"{timestamp}_无极AB_{input_name}.mp4")
            with tempfile.TemporaryDirectory() as temp_dir:
                # 1. 获取主视频分辨率和时长
                self.log("第一步处理中...")
                width, height = self.get_video_resolution(video_b_path)
                duration = self.get_video_duration(video_b_path)
                if duration is None:
                    self.log("  ⚠️ 获取主视频时长失败，默认10秒")
                    duration = 10.0
                duration_str = f"{duration:.2f}"
                # 2. 副视频静音+缩放+补时长
                self.log("第二步处理中...")
                temp_b_silent = os.path.join(temp_dir, "b_silent_scaled.mp4")
                cmd1 = [self.ffmpeg_path, "-y", "-stream_loop", "-1", "-i", video_a_path, "-t", duration_str, "-vf", f"scale={width}:{height}", "-an", "-c:v", "libx264", "-preset", "medium", temp_b_silent]
                self.run_cmd(cmd1)
                # 3. 多轨合成
                self.log("第三步处理中...")
                cmd2 = [self.ffmpeg_path, "-y", "-hide_banner", "-i", temp_b_silent, "-i", video_b_path,
                        "-map", "0:v:0", "-map", "1:v:0", "-map", "1:a:0",
                        "-c:v", "libx264", "-crf", "18", "-preset", "medium", "-c:a", "aac", "-b:a", "320k",
                        "-disposition:v:0", "0", "-disposition:v:1", "default", "-disposition:a:0", "default",
                        "-map_metadata", "-1", final_output_path]
                self.run_cmd(cmd2)
                self.log(f"  ✔️ 完成: {os.path.basename(final_output_path)}")
            if del_a:
                try:
                    os.remove(video_a_path)
                    self.log(f"  ⚠️ 已删除A视频: {video_a_path}")
                except Exception as e:
                    self.log(f"  ⚠️ 删除A视频失败: {e}")
            if del_b:
                try:
                    os.remove(video_b_path)
                    self.log(f"  ⚠️ 已删除B视频: {video_b_path}")
                except Exception as e:
                    self.log(f"  ⚠️ 删除B视频失败: {e}")
        except Exception as e:
            self.log(f"❌ 处理失败: {e}")

    def run_cmd(self, cmd):
        proc = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            encoding='utf-8',
            errors='ignore'
        )
        lines = []
        for line in proc.stdout:
            lines.append(line.strip())
        proc.wait()
        if proc.returncode != 0:
            for line in lines:
                self.log(line)
            self.log(f"命令执行失败：{cmd}")
            raise RuntimeError(f"命令执行失败：{' '.join(cmd)}")
        # 正常时不输出任何FFmpeg日志 