
==================================================
执行时间: 2025-07-30 01:48:07
命令: .\ffmpeg\mkvmerge.exe -o C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\temptack.mkv --timestamps 0:C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\timecodes.txt C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempvideo.h264 --audio-tracks 0 C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempaudio.aac
返回码: 1
标准输出:
mkvmerge v92.0 ('Everglow') 64-bit
'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempvideo.h264': Using the demultiplexer for the format 'AVC/H.264'.
'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempaudio.aac': Using the demultiplexer for the format 'MPEG-1/2 Audio Layer II/III'.
'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempvideo.h264' track 0: Using the output module for the format 'AVC/H.264 (unframed)'.
'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempaudio.aac' track 0: Using the output module for the format 'MPEG-1/2 Audio Layer II/III'.
The file 'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\temptack.mkv' has been opened for writing.
'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempvideo.h264' track 0: Extracted the aspect ratio information from the video bitstream and set the display dimensions to 1048/1935.
Progress: 5%
Progress: 13%
Progress: 29%
Progress: 49%
Warning: 'C:\Users\<USER>\AppData\Local\Temp\tmpikcgtthg\tempvideo.h264' track 0: The number of external timestamps 2926 is smaller than the number of frames in this track. The remaining frames of this track might not be timestamped the way you intended them to be. mkvmerge might even crash.
Progress: 75%
Progress: 85%
Progress: 94%
Progress: 98%
Progress: 100%
Progress: 100%
The cue entries (the index) are being written...
Multiplexing took 7 seconds.

错误输出:


==================================================
执行时间: 2025-07-30 01:53:37
命令: .\ffmpeg\mkvmerge.exe -o C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\temptack.mkv --timestamps 0:C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\timecodes.txt C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempvideo.h264 --audio-tracks 0 C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempaudio.aac
返回码: 1
标准输出:
mkvmerge v92.0 ('Everglow') 64-bit
'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempvideo.h264': Using the demultiplexer for the format 'AVC/H.264'.
'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempaudio.aac': Using the demultiplexer for the format 'MPEG-1/2 Audio Layer II/III'.
'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempvideo.h264' track 0: Using the output module for the format 'AVC/H.264 (unframed)'.
'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempaudio.aac' track 0: Using the output module for the format 'MPEG-1/2 Audio Layer II/III'.
The file 'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\temptack.mkv' has been opened for writing.
'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempvideo.h264' track 0: Extracted the aspect ratio information from the video bitstream and set the display dimensions to 1048/1935.
Progress: 5%
Progress: 13%
Progress: 23%
Progress: 29%
Progress: 43%
Warning: 'C:\Users\<USER>\AppData\Local\Temp\tmpmjshrcwx\tempvideo.h264' track 0: The number of external timestamps 2926 is smaller than the number of frames in this track. The remaining frames of this track might not be timestamped the way you intended them to be. mkvmerge might even crash.
Progress: 53%
Progress: 59%
Progress: 64%
Progress: 68%
Progress: 90%
Progress: 92%
Progress: 94%
Progress: 100%
Progress: 100%
The cue entries (the index) are being written...
Multiplexing took 11 seconds.

错误输出:


==================================================
执行时间: 2025-07-30 01:59:04
命令: .\ffmpeg\mkvmerge.exe -o C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\temptack.mkv --timestamps 0:C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\timecodes.txt C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempvideo.h264 --audio-tracks 0 C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempaudio.aac
返回码: 1
标准输出:
mkvmerge v92.0 ('Everglow') 64-bit
'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempvideo.h264': Using the demultiplexer for the format 'AVC/H.264'.
'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempaudio.aac': Using the demultiplexer for the format 'MPEG-1/2 Audio Layer II/III'.
'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempvideo.h264' track 0: Using the output module for the format 'AVC/H.264 (unframed)'.
'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempaudio.aac' track 0: Using the output module for the format 'MPEG-1/2 Audio Layer II/III'.
The file 'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\temptack.mkv' has been opened for writing.
'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempvideo.h264' track 0: Extracted the aspect ratio information from the video bitstream and set the display dimensions to 1048/1935.
Progress: 5%
Progress: 25%
Warning: 'C:\Users\<USER>\AppData\Local\Temp\tmprkt1a6xw\tempvideo.h264' track 0: The number of external timestamps 2926 is smaller than the number of frames in this track. The remaining frames of this track might not be timestamped the way you intended them to be. mkvmerge might even crash.
Progress: 53%
Progress: 75%
Progress: 80%
Progress: 90%
Progress: 98%
Progress: 100%
Progress: 100%
The cue entries (the index) are being written...
Multiplexing took 6 seconds.

错误输出:


==================================================
执行时间: 2025-07-30 02:02:30
命令: .\ffmpeg\mkvmerge.exe -o C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\temptack.mkv --timestamps 0:C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\timecodes.txt C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempvideo.h264 --audio-tracks 0 C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempaudio.aac
返回码: 1
标准输出:
mkvmerge v92.0 ('Everglow') 64-bit
'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempvideo.h264': Using the demultiplexer for the format 'AVC/H.264'.
'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempaudio.aac': Using the demultiplexer for the format 'MPEG-1/2 Audio Layer II/III'.
'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempvideo.h264' track 0: Using the output module for the format 'AVC/H.264 (unframed)'.
'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempaudio.aac' track 0: Using the output module for the format 'MPEG-1/2 Audio Layer II/III'.
The file 'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\temptack.mkv' has been opened for writing.
'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempvideo.h264' track 0: Extracted the aspect ratio information from the video bitstream and set the display dimensions to 1048/1935.
Progress: 5%
Progress: 25%
Progress: 33%
Progress: 43%
Warning: 'C:\Users\<USER>\AppData\Local\Temp\tmptlbhq95o\tempvideo.h264' track 0: The number of external timestamps 2926 is smaller than the number of frames in this track. The remaining frames of this track might not be timestamped the way you intended them to be. mkvmerge might even crash.
Progress: 53%
Progress: 64%
Progress: 75%
Progress: 80%
Progress: 98%
Progress: 100%
Progress: 100%
The cue entries (the index) are being written...
Multiplexing took 8 seconds.

错误输出:


==================================================
执行时间: 2025-07-30 02:07:30
命令: .\ffmpeg\mkvmerge.exe -o C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\temptack.mkv --timestamps 0:C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\timecodes.txt C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempvideo.h264 --audio-tracks 0 C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempaudio.aac
返回码: 1
标准输出:
mkvmerge v92.0 ('Everglow') 64-bit
'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempvideo.h264': Using the demultiplexer for the format 'AVC/H.264'.
'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempaudio.aac': Using the demultiplexer for the format 'MPEG-1/2 Audio Layer II/III'.
'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempvideo.h264' track 0: Using the output module for the format 'AVC/H.264 (unframed)'.
'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempaudio.aac' track 0: Using the output module for the format 'MPEG-1/2 Audio Layer II/III'.
The file 'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\temptack.mkv' has been opened for writing.
'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempvideo.h264' track 0: Extracted the aspect ratio information from the video bitstream and set the display dimensions to 1048/1935.
Progress: 5%
Progress: 27%
Progress: 33%
Progress: 43%
Progress: 49%
Warning: 'C:\Users\<USER>\AppData\Local\Temp\tmpbke5qp_s\tempvideo.h264' track 0: The number of external timestamps 2926 is smaller than the number of frames in this track. The remaining frames of this track might not be timestamped the way you intended them to be. mkvmerge might even crash.
Progress: 75%
Progress: 85%
Progress: 94%
Progress: 98%
Progress: 100%
Progress: 100%
The cue entries (the index) are being written...
Multiplexing took 8 seconds.

错误输出:

